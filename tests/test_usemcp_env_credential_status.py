import pytest
import uuid
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session
from app.services.marketplace_functions import MarketplaceFunctionService
from app.models.mcp_schema import McpConfig, UserMcpAssignment
from app.utils.constants.constants import (
    EnvCredentialStatus,
    McpVisibility,
    McpStatus,
    McpOwnerType,
)
from app.grpc import mcp_pb2
import grpc


class TestUseMCPEnvCredentialStatus:
    """Test that useMCP correctly sets env_credential_status based on MCP requirements"""

    def setup_method(self):
        self.service = MarketplaceFunctionService()
        self.mock_db = Mock(spec=Session)
        self.mock_context = Mock(spec=grpc.ServicerContext)

    def test_usemcp_with_no_env_keys_required(self):
        """Test useMCP sets NOT_REQUIRED when MCP has no env_keys"""
        # Arrange
        mcp_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())
        
        # Mock MCP with no env_keys
        mock_mcp = McpConfig(
            id=mcp_id,
            name="Test MCP",
            description="Test description",
            visibility=McpVisibility.PUBLIC,
            owner_id=user_id,
            owner_type=McpOwnerType.USER,
            category="general",
            status=McpStatus.ACTIVE,
            env_keys=None,  # No environment variables required
            use_count=0
        )
        
        # Mock database queries
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_mcp
        self.mock_db.query.return_value.filter_by.return_value.first.return_value = None  # No existing assignment
        
        # Mock the assignment that will be created
        mock_assignment = Mock()
        mock_assignment.mcp_id = mcp_id
        
        request = mcp_pb2.UseMCPRequest(mcp_id=mcp_id, user_id=user_id)
        
        with patch.object(self.service, 'get_db', return_value=self.mock_db):
            # Act
            response = self.service.useMCP(request, self.mock_context)
        
        # Assert
        assert response.success is True
        assert "added successfully" in response.message
        
        # Verify UserMcpAssignment was created with correct env_credential_status
        self.mock_db.add.assert_called_once()
        added_assignment = self.mock_db.add.call_args[0][0]
        assert isinstance(added_assignment, UserMcpAssignment)
        assert added_assignment.user_id == user_id
        assert added_assignment.mcp_id == mcp_id
        assert added_assignment.env_credential_status == EnvCredentialStatus.NOT_REQUIRED

    def test_usemcp_with_env_keys_required(self):
        """Test useMCP sets PENDING_INPUT when MCP has env_keys"""
        # Arrange
        mcp_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())
        
        # Mock MCP with env_keys
        mock_mcp = McpConfig(
            id=mcp_id,
            name="Test MCP",
            description="Test description",
            visibility=McpVisibility.PUBLIC,
            owner_id=user_id,
            owner_type=McpOwnerType.USER,
            category="general",
            status=McpStatus.ACTIVE,
            env_keys=[{"key": "GITHUB_TOKEN", "description": "GitHub API token"}],  # Requires env vars
            use_count=0
        )
        
        # Mock database queries
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_mcp
        self.mock_db.query.return_value.filter_by.return_value.first.return_value = None  # No existing assignment
        
        request = mcp_pb2.UseMCPRequest(mcp_id=mcp_id, user_id=user_id)
        
        with patch.object(self.service, 'get_db', return_value=self.mock_db):
            # Act
            response = self.service.useMCP(request, self.mock_context)
        
        # Assert
        assert response.success is True
        assert "added successfully" in response.message
        
        # Verify UserMcpAssignment was created with correct env_credential_status
        self.mock_db.add.assert_called_once()
        added_assignment = self.mock_db.add.call_args[0][0]
        assert isinstance(added_assignment, UserMcpAssignment)
        assert added_assignment.user_id == user_id
        assert added_assignment.mcp_id == mcp_id
        assert added_assignment.env_credential_status == EnvCredentialStatus.PENDING_INPUT

    def test_usemcp_with_existing_assignment(self):
        """Test useMCP returns existing assignment without creating new one"""
        # Arrange
        mcp_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())
        
        mock_mcp = McpConfig(
            id=mcp_id,
            name="Test MCP",
            description="Test description",
            visibility=McpVisibility.PUBLIC,
            owner_id=user_id,
            owner_type=McpOwnerType.USER,
            category="general",
            status=McpStatus.ACTIVE,
            env_keys=None,
            use_count=0
        )
        
        # Mock existing assignment
        existing_assignment = Mock()
        existing_assignment.mcp_id = mcp_id
        
        # Mock database queries
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_mcp
        self.mock_db.query.return_value.filter_by.return_value.first.return_value = existing_assignment
        
        request = mcp_pb2.UseMCPRequest(mcp_id=mcp_id, user_id=user_id)
        
        with patch.object(self.service, 'get_db', return_value=self.mock_db):
            # Act
            response = self.service.useMCP(request, self.mock_context)
        
        # Assert
        assert response.success is True
        assert "already added" in response.message
        
        # Verify no new assignment was created
        self.mock_db.add.assert_not_called()

    def test_usemcp_with_empty_env_keys_list(self):
        """Test useMCP sets NOT_REQUIRED when MCP has empty env_keys list"""
        # Arrange
        mcp_id = str(uuid.uuid4())
        user_id = str(uuid.uuid4())
        
        # Mock MCP with empty env_keys list
        mock_mcp = McpConfig(
            id=mcp_id,
            name="Test MCP",
            description="Test description",
            visibility=McpVisibility.PUBLIC,
            owner_id=user_id,
            owner_type=McpOwnerType.USER,
            category="general",
            status=McpStatus.ACTIVE,
            env_keys=[],  # Empty list - no environment variables required
            use_count=0
        )
        
        # Mock database queries
        self.mock_db.query.return_value.filter.return_value.first.return_value = mock_mcp
        self.mock_db.query.return_value.filter_by.return_value.first.return_value = None
        
        request = mcp_pb2.UseMCPRequest(mcp_id=mcp_id, user_id=user_id)
        
        with patch.object(self.service, 'get_db', return_value=self.mock_db):
            # Act
            response = self.service.useMCP(request, self.mock_context)
        
        # Assert
        assert response.success is True
        
        # Verify UserMcpAssignment was created with NOT_REQUIRED status
        self.mock_db.add.assert_called_once()
        added_assignment = self.mock_db.add.call_args[0][0]
        assert added_assignment.env_credential_status == EnvCredentialStatus.NOT_REQUIRED