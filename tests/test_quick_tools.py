import pytest
import uuid
import grpc
from sqlalchemy.orm import Session
from unittest.mock import MagicMock

from app.models.mcp_schema import McpConfig, UserMcpAssignment
from app.services.mcp_functions import MCPFunctionService
from app.grpc import mcp_pb2
from app.db.session import SessionL<PERSON>al
from app.utils.constants.constants import EnvCredentialStatus, McpVisibility, McpStatus, McpOwnerType, McpCategory


# Helper to create a mock gRPC context
def mock_grpc_context():
    context = MagicMock(spec=grpc.ServicerContext)
    context.set_code = MagicMock()
    context.set_details = MagicMock()
    return context


@pytest.fixture(scope="function")
def db_session():
    """Provides a database session for a test."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.rollback()  # Ensure no changes persist
        db.close()


@pytest.fixture(scope="function")
def mcp_function_service():
    """Provides an instance of MCPFunctionService."""
    return MCPFunctionService()


@pytest.fixture(scope="function")
def test_mcp_and_user(db_session: Session):
    """Creates a test MCP and user assignment for testing."""
    mcp_id = str(uuid.uuid4())
    user_id = str(uuid.uuid4())
    owner_id = str(uuid.uuid4())
    
    # Create MCP
    mcp_config = McpConfig(
        id=mcp_id,
        name="Test MCP for Quick Tools",
        description="A test MCP",
        owner_id=owner_id,
        owner_type=McpOwnerType.USER,
        visibility=McpVisibility.PRIVATE,
        status=McpStatus.ACTIVE,
        category=McpCategory.GENERAL
    )
    db_session.add(mcp_config)
    
    # Create user assignment
    assignment = UserMcpAssignment(
        id=str(uuid.uuid4()),
        user_id=user_id,
        mcp_id=mcp_id,
        env_credential_status=EnvCredentialStatus.NOT_REQUIRED,
        is_quick_tool=False
    )
    db_session.add(assignment)
    db_session.commit()
    
    return {
        "mcp_id": mcp_id,
        "user_id": user_id,
        "owner_id": owner_id,
        "mcp_config": mcp_config,
        "assignment": assignment
    }


def test_make_quick_tool_success(db_session: Session, mcp_function_service: MCPFunctionService, test_mcp_and_user):
    """Test successfully making an MCP a quick tool."""
    context = mock_grpc_context()
    request = mcp_pb2.MakeQuickToolRequest(
        mcp_id=test_mcp_and_user["mcp_id"],
        user_id=test_mcp_and_user["user_id"]
    )
    
    response = mcp_function_service.makeQuickTool(request, context)
    
    assert response.success is True
    assert "successfully added to quick tools" in response.message
    assert response.quick_tools_count == 1
    
    # Verify in database
    assignment = db_session.query(UserMcpAssignment).filter(
        UserMcpAssignment.mcp_id == test_mcp_and_user["mcp_id"],
        UserMcpAssignment.user_id == test_mcp_and_user["user_id"]
    ).first()
    assert assignment.is_quick_tool is True


def test_make_quick_tool_already_quick_tool(db_session: Session, mcp_function_service: MCPFunctionService, test_mcp_and_user):
    """Test making an MCP a quick tool when it's already a quick tool."""
    # First make it a quick tool
    assignment = db_session.query(UserMcpAssignment).filter(
        UserMcpAssignment.mcp_id == test_mcp_and_user["mcp_id"],
        UserMcpAssignment.user_id == test_mcp_and_user["user_id"]
    ).first()
    assignment.is_quick_tool = True
    db_session.commit()
    
    context = mock_grpc_context()
    request = mcp_pb2.MakeQuickToolRequest(
        mcp_id=test_mcp_and_user["mcp_id"],
        user_id=test_mcp_and_user["user_id"]
    )
    
    response = mcp_function_service.makeQuickTool(request, context)
    
    assert response.success is True
    assert "already a quick tool" in response.message


def test_make_quick_tool_mcp_not_found(db_session: Session, mcp_function_service: MCPFunctionService):
    """Test making a non-existent MCP a quick tool."""
    context = mock_grpc_context()
    request = mcp_pb2.MakeQuickToolRequest(
        mcp_id=str(uuid.uuid4()),  # Non-existent MCP
        user_id=str(uuid.uuid4())
    )
    
    response = mcp_function_service.makeQuickTool(request, context)
    
    assert response.success is False
    assert "MCP not found" in response.message
    context.set_code.assert_called_with(grpc.StatusCode.NOT_FOUND)


def test_make_quick_tool_user_no_access(db_session: Session, mcp_function_service: MCPFunctionService, test_mcp_and_user):
    """Test making an MCP a quick tool when user has no access."""
    context = mock_grpc_context()
    request = mcp_pb2.MakeQuickToolRequest(
        mcp_id=test_mcp_and_user["mcp_id"],
        user_id=str(uuid.uuid4())  # Different user with no access
    )
    
    response = mcp_function_service.makeQuickTool(request, context)
    
    assert response.success is False
    assert "does not have access" in response.message
    context.set_code.assert_called_with(grpc.StatusCode.NOT_FOUND)


def test_make_quick_tool_limit_exceeded(db_session: Session, mcp_function_service: MCPFunctionService):
    """Test making an MCP a quick tool when user has reached the 4-tool limit."""
    user_id = str(uuid.uuid4())
    owner_id = str(uuid.uuid4())
    
    # Create 4 MCPs and make them quick tools
    for i in range(4):
        mcp_id = str(uuid.uuid4())
        mcp_config = McpConfig(
            id=mcp_id,
            name=f"Test MCP {i}",
            description=f"Test MCP {i}",
            owner_id=owner_id,
            owner_type=McpOwnerType.USER,
            visibility=McpVisibility.PRIVATE,
            status=McpStatus.ACTIVE,
            category=McpCategory.GENERAL
        )
        db_session.add(mcp_config)
        
        assignment = UserMcpAssignment(
            id=str(uuid.uuid4()),
            user_id=user_id,
            mcp_id=mcp_id,
            env_credential_status=EnvCredentialStatus.NOT_REQUIRED,
            is_quick_tool=True
        )
        db_session.add(assignment)
    
    # Create a 5th MCP
    mcp_id_5 = str(uuid.uuid4())
    mcp_config_5 = McpConfig(
        id=mcp_id_5,
        name="Test MCP 5",
        description="Test MCP 5",
        owner_id=owner_id,
        owner_type=McpOwnerType.USER,
        visibility=McpVisibility.PRIVATE,
        status=McpStatus.ACTIVE,
        category=McpCategory.GENERAL
    )
    db_session.add(mcp_config_5)
    
    assignment_5 = UserMcpAssignment(
        id=str(uuid.uuid4()),
        user_id=user_id,
        mcp_id=mcp_id_5,
        env_credential_status=EnvCredentialStatus.NOT_REQUIRED,
        is_quick_tool=False
    )
    db_session.add(assignment_5)
    db_session.commit()
    
    context = mock_grpc_context()
    request = mcp_pb2.MakeQuickToolRequest(
        mcp_id=mcp_id_5,
        user_id=user_id
    )
    
    response = mcp_function_service.makeQuickTool(request, context)
    
    assert response.success is False
    assert "maximum limit of 4 quick tools" in response.message
    assert response.quick_tools_count == 4
    context.set_code.assert_called_with(grpc.StatusCode.FAILED_PRECONDITION)


def test_remove_quick_tool_success(db_session: Session, mcp_function_service: MCPFunctionService, test_mcp_and_user):
    """Test successfully removing an MCP from quick tools."""
    # First make it a quick tool
    assignment = db_session.query(UserMcpAssignment).filter(
        UserMcpAssignment.mcp_id == test_mcp_and_user["mcp_id"],
        UserMcpAssignment.user_id == test_mcp_and_user["user_id"]
    ).first()
    assignment.is_quick_tool = True
    db_session.commit()
    
    context = mock_grpc_context()
    request = mcp_pb2.RemoveQuickToolRequest(
        mcp_id=test_mcp_and_user["mcp_id"],
        user_id=test_mcp_and_user["user_id"]
    )
    
    response = mcp_function_service.removeQuickTool(request, context)
    
    assert response.success is True
    assert "successfully removed from quick tools" in response.message
    assert response.quick_tools_count == 0
    
    # Verify in database
    assignment = db_session.query(UserMcpAssignment).filter(
        UserMcpAssignment.mcp_id == test_mcp_and_user["mcp_id"],
        UserMcpAssignment.user_id == test_mcp_and_user["user_id"]
    ).first()
    assert assignment.is_quick_tool is False


def test_remove_quick_tool_not_quick_tool(db_session: Session, mcp_function_service: MCPFunctionService, test_mcp_and_user):
    """Test removing an MCP from quick tools when it's not a quick tool."""
    context = mock_grpc_context()
    request = mcp_pb2.RemoveQuickToolRequest(
        mcp_id=test_mcp_and_user["mcp_id"],
        user_id=test_mcp_and_user["user_id"]
    )
    
    response = mcp_function_service.removeQuickTool(request, context)
    
    assert response.success is True
    assert "not currently a quick tool" in response.message


def test_remove_quick_tool_user_no_access(db_session: Session, mcp_function_service: MCPFunctionService, test_mcp_and_user):
    """Test removing an MCP from quick tools when user has no access."""
    context = mock_grpc_context()
    request = mcp_pb2.RemoveQuickToolRequest(
        mcp_id=test_mcp_and_user["mcp_id"],
        user_id=str(uuid.uuid4())  # Different user with no access
    )
    
    response = mcp_function_service.removeQuickTool(request, context)
    
    assert response.success is False
    assert "does not have access" in response.message
    context.set_code.assert_called_with(grpc.StatusCode.NOT_FOUND)


def test_list_mcps_quick_tools_only_filter(db_session: Session, mcp_function_service: MCPFunctionService):
    """Test listing MCPs with quick_tools_only filter."""
    # Clean up any existing data first
    db_session.query(UserMcpAssignment).delete()
    db_session.query(McpConfig).delete()
    db_session.commit()

    user_id = str(uuid.uuid4())
    owner_id = str(uuid.uuid4())
    
    # Create 3 MCPs, make 2 of them quick tools
    mcp_ids = []
    for i in range(3):
        mcp_id = str(uuid.uuid4())
        mcp_ids.append(mcp_id)
        
        mcp_config = McpConfig(
            id=mcp_id,
            name=f"Test MCP {i}",
            description=f"Test MCP {i}",
            owner_id=owner_id,
            owner_type=McpOwnerType.USER,
            visibility=McpVisibility.PRIVATE,
            status=McpStatus.ACTIVE,
            category=McpCategory.GENERAL
        )
        db_session.add(mcp_config)
        
        assignment = UserMcpAssignment(
            id=str(uuid.uuid4()),
            user_id=user_id,
            mcp_id=mcp_id,
            env_credential_status=EnvCredentialStatus.NOT_REQUIRED,
            is_quick_tool=(i < 2)  # First 2 are quick tools
        )
        db_session.add(assignment)
    
    db_session.commit()
    
    context = mock_grpc_context()
    request = mcp_pb2.ListMCPsRequest(
        page=1,
        page_size=10,
        owner_id=user_id,
        quick_tools_only=True
    )
    
    # Patch the service to use the test database session
    original_get_db = mcp_function_service.get_db
    mcp_function_service.get_db = lambda: db_session

    try:
        response = mcp_function_service.listMCPs(request, context)
    finally:
        # Restore the original get_db method
        mcp_function_service.get_db = original_get_db

    assert response.total == 2  # Only 2 quick tools
    assert len(response.mcps) == 2

    # Verify all returned MCPs are quick tools
    for mcp in response.mcps:
        assert mcp.is_quick_tool is True
