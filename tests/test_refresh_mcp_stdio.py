import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from sqlalchemy.orm import Session
from app.services.mcp_functions import MCPFunctionService
from app.models.mcp_schema import McpConfig
from app.grpc import mcp_pb2
from app.utils.constants.constants import DeploymentStatus
import grpc


class TestRefreshMCPStdio:
    """Test cases for refresh_mcp function with stdio type MCPs"""

    @pytest.fixture
    def mcp_service(self):
        return MCPFunctionService()

    @pytest.fixture
    def mock_db(self):
        return Mock(spec=Session)

    @pytest.fixture
    def mock_context(self):
        context = Mock(spec=grpc.ServicerContext)
        return context

    @pytest.fixture
    def stdio_mcp_config(self):
        """MCP config with stdio type and image_name"""
        mcp = Mock(spec=McpConfig)
        mcp.id = "test-mcp-id"
        mcp.name = "Test STDIO MCP"
        mcp.image_name = "test-image:latest"
        mcp.owner_id = "test-user-id"
        mcp.config = [{"type": "stdio", "image_name": "test-image:latest"}]
        return mcp

    @pytest.fixture
    def stdio_mcp_config_no_image(self):
        """MCP config with stdio type but no image_name"""
        mcp = Mock(spec=McpConfig)
        mcp.id = "test-mcp-id"
        mcp.name = "Test STDIO MCP No Image"
        mcp.image_name = None
        mcp.owner_id = "test-user-id"
        mcp.config = [{"type": "stdio"}]
        return mcp

    @patch('app.services.mcp_functions.save_tools_to_db')
    @patch('app.services.mcp_functions.tools_to_json_response_stdio')
    @patch('app.services.mcp_functions.SSHMCPClient')
    @patch('app.services.mcp_functions.SSHDockerService')
    @patch('app.services.mcp_functions.settings')
    def test_refresh_mcp_stdio_success(
        self, 
        mock_settings,
        mock_ssh_docker_service,
        mock_ssh_mcp_client,
        mock_tools_to_json_response_stdio,
        mock_save_tools_to_db,
        mcp_service,
        mock_db,
        mock_context,
        stdio_mcp_config
    ):
        """Test successful refresh of stdio type MCP"""
        # Setup mocks
        mock_settings.DEFAULT_SSH_HOST = "test-host"
        mock_settings.DEFAULT_SSH_USER = "test-user"
        mock_settings.DEFAULT_SSH_KEY_CONTENT = "test-key"
        
        # Mock the get_db method to return our mock_db
        with patch.object(mcp_service, 'get_db', return_value=mock_db):
            mock_db.query.return_value.filter.return_value.first.return_value = stdio_mcp_config
        
        # Mock SSH Docker Service
        mock_ssh_service_instance = AsyncMock()
        mock_ssh_docker_service.return_value.__aenter__.return_value = mock_ssh_service_instance
        mock_ssh_service_instance.create_container.return_value = "container-id"
        mock_ssh_service_instance.stop_container.return_value = True
        mock_ssh_service_instance.delete_container.return_value = True
        
        # Mock SSH MCP Client
        mock_ssh_client_instance = AsyncMock()
        mock_ssh_mcp_client.return_value = mock_ssh_client_instance
        mock_ssh_client_instance.fetch_tools.return_value = [{"name": "test_tool", "description": "Test tool"}]
        
        # Mock tools processing
        mock_tools_json = {"tools": [{"name": "test_tool", "description": "Test tool"}]}
        mock_tools_to_json_response_stdio.return_value = mock_tools_json
        
        # Mock protobuf conversion
        with patch.object(mcp_service, '_mcp_to_protobuf') as mock_to_protobuf:
            mock_to_protobuf.return_value = Mock()
            
            # Create request
            request = mcp_pb2.RefreshMCPRequest(mcp_id="test-mcp-id")
            
            # Execute
            response = mcp_service.refresh_mcp(request, mock_context)
            
            # Assertions
            assert response.success is True
            assert "Tools refreshed successfully" in response.message
            mock_save_tools_to_db.assert_called_once_with(mock_db, stdio_mcp_config.id, mock_tools_json)
            mock_db.commit.assert_called_once()

    def test_refresh_mcp_stdio_no_image_name(
        self,
        mcp_service,
        mock_db,
        mock_context,
        stdio_mcp_config_no_image
    ):
        """Test refresh of stdio type MCP without image_name should skip stdio processing"""
        # Mock the get_db method to return our mock_db
        with patch.object(mcp_service, 'get_db', return_value=mock_db):
            mock_db.query.return_value.filter.return_value.first.return_value = stdio_mcp_config_no_image
            
            request = mcp_pb2.RefreshMCPRequest(mcp_id="test-mcp-id")
            
            response = mcp_service.refresh_mcp(request, mock_context)
        
        # Should fail because no valid URL types were processed
        assert response.success is False
        assert "Tool retrieval failed" in response.message

    @patch('app.services.mcp_functions.SSHDockerService')
    @patch('app.services.mcp_functions.settings')
    def test_refresh_mcp_stdio_container_failure(
        self,
        mock_settings,
        mock_ssh_docker_service,
        mcp_service,
        mock_db,
        mock_context,
        stdio_mcp_config
    ):
        """Test refresh of stdio type MCP when container operations fail"""
        # Setup mocks
        mock_settings.DEFAULT_SSH_HOST = "test-host"
        mock_settings.DEFAULT_SSH_USER = "test-user"
        mock_settings.DEFAULT_SSH_KEY_CONTENT = "test-key"
        
        # Mock the get_db method to return our mock_db
        with patch.object(mcp_service, 'get_db', return_value=mock_db):
            mock_db.query.return_value.filter.return_value.first.return_value = stdio_mcp_config
            
            # Mock SSH Docker Service to raise exception
            mock_ssh_service_instance = AsyncMock()
            mock_ssh_docker_service.return_value.__aenter__.return_value = mock_ssh_service_instance
            mock_ssh_service_instance.create_container.side_effect = Exception("Container creation failed")
            
            request = mcp_pb2.RefreshMCPRequest(mcp_id="test-mcp-id")
            
            response = mcp_service.refresh_mcp(request, mock_context)
        
        # Should fail due to container operation failure
        assert response.success is False
        assert "Container creation failed" in response.message

    def test_refresh_mcp_mcp_not_found(
        self,
        mcp_service,
        mock_db,
        mock_context
    ):
        """Test refresh when MCP is not found"""
        # Mock the get_db method to return our mock_db
        with patch.object(mcp_service, 'get_db', return_value=mock_db):
            mock_db.query.return_value.filter.return_value.first.return_value = None
            
            request = mcp_pb2.RefreshMCPRequest(mcp_id="non-existent-id")
            
            response = mcp_service.refresh_mcp(request, mock_context)
        
        assert response.success is False
        assert "MCP not found" in response.message
        mock_context.set_code.assert_called_with(grpc.StatusCode.NOT_FOUND)

    def test_refresh_mcp_empty_config(
        self,
        mcp_service,
        mock_db,
        mock_context
    ):
        """Test refresh when MCP has empty config"""
        mcp = Mock(spec=McpConfig)
        mcp.id = "test-mcp-id"
        mcp.config = []
        
        # Mock the get_db method to return our mock_db
        with patch.object(mcp_service, 'get_db', return_value=mock_db):
            mock_db.query.return_value.filter.return_value.first.return_value = mcp
            
            request = mcp_pb2.RefreshMCPRequest(mcp_id="test-mcp-id")
            
            response = mcp_service.refresh_mcp(request, mock_context)
        
        assert response.success is False
        assert "MCP config is empty" in response.message
        mock_context.set_code.assert_called_with(grpc.StatusCode.FAILED_PRECONDITION)