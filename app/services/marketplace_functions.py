# app/services/mcp_config_service.py
import asyncio
from datetime import datetime, timezone
import shlex
import uuid
import grpc
import json
import structlog
from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, or_
from app.db.session import SessionLocal
from app.models.mcp_schema import McpConfig, McpDeployment
from app.models.mcp_rating import McpRating
from app.grpc import mcp_pb2, mcp_pb2_grpc
from app.services.mcp_functions import get_tools_json_from_db
from app.utils.constants.constants import (
    DeploymentStatus,
    McpComponentCategory,
    McpStatus,
    McpVisibility,
    McpCategory,
    UrlType,
    EnvCredentialStatus, # Import EnvCredentialStatus
)
from app.utils.kafka.kafka_service import KafkaProducer

from app.utils.MCP.fetch_tools import get_mcp_tools, tools_to_json_response

from app.utils.google_pubsub import publish_deployment_message

from app.core.config import settings


from app.utils.stdio_deployment import SSHDockerService

from app.utils.MCP.fetch_tools_stdio import SSHMCPClient


logger = structlog.get_logger(__name__)


class MarketplaceFunctionService(mcp_pb2_grpc.MCPServiceServicer):
    def __init__(self):
        self.kafka_producer = KafkaProducer()

    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def _mcp_to_marketplace_mcp(self,db: Session, mcp: McpConfig) -> mcp_pb2.MarketplaceMCP:
        """
        Converts a McpConfig model instance to its MarketplaceMCP protobuf representation.

        Args:
            mcp (McpConfig): The MCP configuration model instance

        Returns:
            mcp_pb2.MarketplaceMCP: The marketplace protobuf representation of the MCP
        """
        # Extract capabilities from mcp_tools_config if available
        capabilities = []
        mcp_tools_config = get_tools_json_from_db(db, mcp.id)
        if mcp_tools_config and isinstance(mcp_tools_config, dict):
            tools = mcp_tools_config.get("tools", [])
            if isinstance(tools, list):
                capabilities = [tool.get("name", "") for tool in tools if isinstance(tool, dict)]

        return mcp_pb2.MarketplaceMCP(
            id=str(mcp.id),
            name=mcp.name,
            description=mcp.description,
            logo=mcp.logo if mcp.logo else "",
            mcp_tools_config=json.dumps(mcp_tools_config),
            visibility=mcp.visibility,
            owner_id=mcp.owner_id,
            owner_type=mcp.owner_type if mcp.owner_type else "",
            user_ids=mcp.user_ids if mcp.user_ids else [],
            category=mcp.category,
            tags=mcp.tags if mcp.tags else [],
            status=mcp.status,
            config=json.dumps(mcp.config) if mcp.config else None,
            git_url=mcp.git_url if mcp.git_url else "",
            created_at=mcp.created_at.isoformat(),
            updated_at=mcp.updated_at.isoformat(),
            average_rating=mcp.average_rating if mcp.average_rating else 0.0,
            use_count=mcp.use_count if mcp.use_count else 0,
            api_documentation="",  
            capabilities=capabilities,
            env_keys=mcp.env_keys if mcp.env_keys else None,
        )

    def getMarketplaceMCPs(
        self, request: mcp_pb2.GetMarketplaceMCPsRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.GetMarketplaceMCPsResponse:
        """
        Retrieves a paginated list of public MCP configurations for the marketplace.

        Args:
            request: Contains pagination, search, and filter parameters
            context: gRPC service context

        Returns:
            Response containing a list of marketplace MCPs
        """
        db = self.get_db()
        try:
            logger.info("get_marketplace_mcps_request", request=request)

            # Start with base query for public MCPs
            query = db.query(McpConfig).filter(
                McpConfig.visibility == McpVisibility.PUBLIC, McpConfig.status == McpStatus.ACTIVE
            )

            # Apply search filter if provided
            if request.HasField("search") and request.search:
                search_term = f"%{request.search}%"
                query = query.filter(
                    or_(McpConfig.name.ilike(search_term), McpConfig.description.ilike(search_term))
                )

            # Apply category filter if provided
            if request.HasField("category") and request.category:
                try:
                    category_enum = McpCategory[request.category.upper()]
                    query = query.filter(McpConfig.category == category_enum)
                except KeyError:
                    raise ValueError(f"Invalid MCP category: {request.category}")

            if request.HasField("component_category") and request.component_category:
                try:
                    component_category = McpComponentCategory[request.component_category.upper()]
                    query = query.filter(McpConfig.component_category == component_category)
                except KeyError:
                    raise ValueError(f"Invalid MCP component category: {request.component_category}")
            
            # Apply tags filter if provided
            if request.tags:
                # Convert tags to a list if it's not already
                tags_list = (
                    list(request.tags) if hasattr(request.tags, "__iter__") else [request.tags]
                )
                # Use ANY to check if any of the requested tags are in the MCP's tags array
                for tag in tags_list:
                    query = query.filter(McpConfig.tags.any(tag))

            # Apply sorting
            if request.HasField("sort_by"):
                sort_by = request.sort_by
                if sort_by == "newest":
                    query = query.order_by(desc(McpConfig.created_at))
                elif sort_by == "oldest":
                    query = query.order_by(asc(McpConfig.created_at))
                elif sort_by == "most_popular":
                    # Placeholder for popularity-based sorting
                    # In a real implementation, you might have a downloads or usage count field
                    query = query.order_by(desc(McpConfig.created_at))
                elif sort_by == "highest_rated":
                    # Placeholder for rating-based sorting
                    # In a real implementation, you would join with a ratings table
                    query = query.order_by(desc(McpConfig.created_at))
            else:
                # Default sort by newest
                query = query.order_by(desc(McpConfig.created_at))

            # Get total count
            total = query.count()

            # Apply pagination
            page = request.page if request.page > 0 else 1
            page_size = request.page_size if request.page_size > 0 else 10

            mcps = query.offset((page - 1) * page_size).limit(page_size).all()

            # Calculate pagination metadata
            total_pages = (total + page_size - 1) // page_size
            has_next = page < total_pages
            has_prev = page > 1
            next_page = page + 1 if has_next else None
            prev_page = page - 1 if has_prev else None

            # Convert to marketplace MCP format
            marketplace_mcps = [self._mcp_to_marketplace_mcp(db,mcp) for mcp in mcps]

            return mcp_pb2.GetMarketplaceMCPsResponse(
                success=True,
                message="Marketplace MCPs retrieved successfully",
                mcps=marketplace_mcps,
                total=total,
                page=page,
                page_size=page_size,
                total_pages=total_pages,
                has_next=has_next,
                has_prev=has_prev,
                next_page=next_page,
                prev_page=prev_page,
            )

        except Exception as e:
            logger.error("get_marketplace_mcps_error", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return mcp_pb2.GetMarketplaceMCPsResponse(
                success=False, message="Failed to retrieve marketplace MCPs"
            )
        finally:
            db.close()

    def getMarketplaceMCPDetail(
        self, request: mcp_pb2.GetMarketplaceMCPDetailRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.GetMarketplaceMCPDetailResponse:
        """
        Retrieves detailed information about a specific marketplace MCP.

        Args:
            request: Contains the ID of the marketplace MCP to retrieve and optional user_id
            context: gRPC service context

        Returns:
            Response containing detailed information about the marketplace MCP with is_added flag
        """
        db = self.get_db()
        try:
            logger.info("get_marketplace_mcp_detail_request", mcp_id=request.id)

            # Find the MCP
            mcp_config = (
                db.query(McpConfig)
                .filter(
                    McpConfig.id == request.id,
                    McpConfig.visibility == McpVisibility.PUBLIC,
                    McpConfig.status == McpStatus.ACTIVE,
                )
                .first()
            )

            if not mcp_config:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Marketplace MCP with ID {request.id} not found")
                return mcp_pb2.GetMarketplaceMCPDetailResponse(
                    success=False, message=f"Marketplace MCP with ID {request.id} not found"
                )

            # Convert to marketplace MCP format
            marketplace_mcp = self._mcp_to_marketplace_mcp(db,mcp_config)

            # Check if user_id is provided and if the user has already added this MCP to their workspace
            is_added = False
            env_credential_status = EnvCredentialStatus.PENDING_INPUT.value # Default to PENDING_INPUT
            try:
                # Check if user_id field exists and is provided
                if hasattr(request, "user_id") and request.HasField("user_id") and request.user_id:
                    from app.models.mcp_schema import UserMcpAssignment

                    # Determine initial status based on whether env_keys are required for the MCP
                    if not mcp_config.env_keys:
                        env_credential_status = EnvCredentialStatus.NOT_REQUIRED.value
                    else:
                        env_credential_status = EnvCredentialStatus.PENDING_INPUT.value

                    # Check if there's an assignment in UserMcpAssignment table
                    existing_assignment = (
                        db.query(UserMcpAssignment)
                        .filter_by(user_id=request.user_id, mcp_id=str(mcp_config.id))
                        .first()
                    )
                    is_added = existing_assignment is not None
                    
                    is_added = mcp_config.owner_id == request.user_id or existing_assignment is not None

                    # If an assignment exists, use its status, otherwise use the determined initial status
                    env_credential_status = (
                        existing_assignment.env_credential_status.value
                        if existing_assignment
                        else env_credential_status # Use the status determined based on env_keys
                    )

                    print("env_credential_status: ", env_credential_status)
                    
                    logger.info(
                        "checking_if_user_added_marketplace_mcp_to_workspace",
                        user_id=request.user_id,
                        mcp_id=mcp_config.id,
                        is_added=is_added,
                        assignment_exists=existing_assignment is not None,
                    )
            except Exception as e:
                # Fallback: if user_id field doesn't exist or there's an error, default to False
                logger.warning(
                    "error_checking_user_mcp_assignment_detail",
                    mcp_id=mcp_config.id,
                    error=str(e),
                    fallback_is_added=False,
                )
                is_added = False

            # Set the is_added field
            marketplace_mcp.is_added = is_added
            marketplace_mcp.env_credential_status = env_credential_status
            return mcp_pb2.GetMarketplaceMCPDetailResponse(
                success=True,
                message="Marketplace MCP details retrieved successfully",
                mcp=marketplace_mcp,
            )

        except Exception as e:
            logger.error("get_marketplace_mcp_detail_error", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return mcp_pb2.GetMarketplaceMCPDetailResponse(
                success=False, message="Failed to retrieve marketplace MCP details"
            )
        finally:
            db.close()

    def rateMCP(
        self, request: mcp_pb2.RateMCPRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.RateMCPResponse:
        """
        Rate an MCP and update its average rating.

        Args:
            request: Contains the MCP ID, user ID, and rating value
            context: gRPC service context

        Returns:
            Response containing success status, message, and updated average rating
        """
        db = self.get_db()
        try:
            logger.info(
                "rate_mcp_request",
                mcp_id=request.mcp_id,
                user_id=request.user_id,
                rating=request.rating,
            )

            # Validate rating value (between 1.0 and 5.0)
            if request.rating < 1.0 or request.rating > 5.0:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Rating must be between 1.0 and 5.0")
                return mcp_pb2.RateMCPResponse(
                    success=False, message="Rating must be between 1.0 and 5.0", average_rating=0.0
                )

            # Check if MCP exists
            mcp = db.query(McpConfig).filter(McpConfig.id == request.mcp_id).first()
            if not mcp:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"MCP with ID {request.mcp_id} not found")
                return mcp_pb2.RateMCPResponse(
                    success=False,
                    message=f"MCP with ID {request.mcp_id} not found",
                    average_rating=0.0,
                )

            # Check if user has already rated this MCP
            existing_rating = (
                db.query(McpRating)
                .filter(McpRating.mcp_id == request.mcp_id, McpRating.user_id == request.user_id)
                .first()
            )

            if existing_rating:
                # Update existing rating
                old_rating = existing_rating.rating
                existing_rating.rating = request.rating
                existing_rating.updated_at = datetime.now(timezone.utc)
                db.commit()
                logger.info(
                    "updated_mcp_rating",
                    mcp_id=request.mcp_id,
                    user_id=request.user_id,
                    old_rating=old_rating,
                    new_rating=request.rating,
                )
            else:
                # Create new rating
                new_rating = McpRating(
                    mcp_id=request.mcp_id, user_id=request.user_id, rating=request.rating
                )
                db.add(new_rating)
                db.commit()
                logger.info(
                    "created_mcp_rating",
                    mcp_id=request.mcp_id,
                    user_id=request.user_id,
                    rating=request.rating,
                )

            # Calculate new average rating
            ratings = db.query(McpRating).filter(McpRating.mcp_id == request.mcp_id).all()
            total_rating = sum(r.rating for r in ratings)
            average_rating = total_rating / len(ratings) if ratings else 0.0

            # Update MCP with new average rating
            mcp.average_rating = average_rating
            mcp.updated_at = datetime.now(timezone.utc)
            db.commit()

            return mcp_pb2.RateMCPResponse(
                success=True,
                message=f"Rating for MCP {mcp.name} updated successfully",
                average_rating=average_rating,
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error rating MCP: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return mcp_pb2.RateMCPResponse(
                success=False, message="Failed to update MCP rating", average_rating=0.0
            )
        finally:
            db.close()

    def useMCP(
        self, request: mcp_pb2.UseMCPRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.UseMCPResponse:
        """
        Record usage of an MCP and increment its use count.

        Args:
            request: Contains the MCP ID and user ID
            context: gRPC service context

        Returns:
            Response containing success status, message, and updated use count
        """
        db = self.get_db()
        try:
            logger.info("use_mcp_request", mcp_id=request.mcp_id, user_id=request.user_id)

            mcp = db.query(McpConfig).filter(McpConfig.id == request.mcp_id).first()
            if not mcp:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"MCP with ID {request.mcp_id} not found")
                return mcp_pb2.UseMCPResponse(
                    success=False, message=f"MCP with ID {request.mcp_id} not found", use_count=0
                )

            if mcp.visibility != McpVisibility.PUBLIC and request.user_id != mcp.owner_id:
                if not mcp.user_ids or request.user_id not in mcp.user_ids:
                    context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                    context.set_details("User is not authorized to use this MCP")
                    return mcp_pb2.UseMCPResponse(
                        success=False,
                        message="User is not authorized to use this MCP",
                        use_count=mcp.use_count,
                    )

            from app.models.mcp_schema import UserMcpAssignment

            assignment = (
                db.query(UserMcpAssignment)
                .filter_by(user_id=request.user_id, mcp_id=request.mcp_id)
                .first()
            )

            if assignment:
                message = f"You have already added MCP {mcp.name} to your workspace"
                mcp_id = assignment.mcp_id
            else:
                # Determine the correct env_credential_status based on MCP requirements
                if not mcp.env_keys:
                    # No environment variables required
                    env_credential_status = EnvCredentialStatus.NOT_REQUIRED
                else:
                    # Environment variables are required, user needs to provide them
                    env_credential_status = EnvCredentialStatus.PENDING_INPUT
                
                assignment = UserMcpAssignment(
                    user_id=request.user_id,
                    mcp_id=request.mcp_id,
                    env_credential_status=env_credential_status
                )
                db.add(assignment)
                db.commit()
                message = f"MCP {mcp.name} added successfully to workspace"
                mcp_id = assignment.mcp_id

            mcp.use_count += 1
            mcp.updated_at = datetime.now(timezone.utc)
            db.commit()

            logger.info(
                "mcp_usage_recorded",
                mcp_id=request.mcp_id,
                user_id=request.user_id,
                new_use_count=mcp.use_count,
            )

            return mcp_pb2.UseMCPResponse(
                success=True,
                message=message,
                use_count=mcp.use_count,
                mcp_id=mcp_id,
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error recording MCP usage: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return mcp_pb2.UseMCPResponse(
                success=False, message="Failed to record MCP usage", use_count=0
            )
        finally:
            db.close()
