# app/services/mcp_config_service.py
import copy
import asyncio
from datetime import datetime, timezone
import shlex
import uuid
import grpc
import json
import structlog
from sqlalchemy.orm import Session
from sqlalchemy import func, select
from sqlalchemy import desc, asc, or_
from app.db.session import SessionLocal
from app.models.mcp_schema import McpConfig, McpDeployment, McpToolConfig, UserMcpAssignment
from app.models.mcp_rating import McpRating
from app.grpc import mcp_pb2, mcp_pb2_grpc
from app.utils.constants.constants import (
    DeploymentStatus,
    EnvCredentialStatus,
    McpComponentCategory,
    McpStatus,
    McpVisibility,
    McpCategory,
    UrlType,
    ContainerStatus,
)
from app.utils.kafka.kafka_service import KafkaProducer

from app.utils.MCP.fetch_tools import (
    get_mcp_tools,
    tools_to_json_response,
    tools_to_json_response_stdio,
)

from app.utils.MCP.mcp_client_http import (
    get_mcp_tools_async,
    run_async_from_sync,
    tools_to_json_response_http,
)

from app.utils.MCP.mcp_client_http import MCPToolsLister

from app.utils.google_pubsub import publish_deployment_message

from app.core.config import settings


from app.utils.stdio_deployment import SSHDockerService

from app.utils.MCP.fetch_tools_stdio import SSHMCPClient

from app.utils.secret_manager.secret_manager import EncryptionManager

logger = structlog.get_logger(__name__)

secret_manager = EncryptionManager()


class MCPFunctionService(mcp_pb2_grpc.MCPServiceServicer):
    def __init__(self):
        self.kafka_producer = KafkaProducer()

    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def createMCP(
        self, request: mcp_pb2.CreateMCPRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.MCPResponse:
        db = self.get_db()
        logger.info(f"gRPC: create_mcp_request received for name: {request.name}")

        try:
            mcp_type = request.mcp_type if request.mcp_type else None
            print(f"[DEBUG-> MCP TYPE] {mcp_type}")
            config = None
            tools_json = None
            is_git_deployment_scenario = False
            tools_retrieval_success = False
            tools_error_message = None

            # --- Scenario 1: config present (primary) ---
            if request.config:
                try:
                    config = [{"url": u.url, "type": u.type} for u in request.config]
                except json.JSONDecodeError:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details("Invalid JSON in request.config")
                    return mcp_pb2.MCPResponse(
                        success=False, message="Invalid JSON in request.config"
                    )

                # Validate structure: must be a non-empty list of dicts with 'url' and 'type'
                if (
                    not isinstance(config, list)
                    or not config
                    or not all("url" in u and "type" in u for u in config)
                ):
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(
                        "Each url must have 'url' and 'type', and at least one url must be provided"
                    )
                    return mcp_pb2.MCPResponse(success=False, message="Invalid config structure")

                # Optionally: Only process the first SSE url for tools
                for url in config:
                    if url.get("type") == "sse" and "url" in url:
                        sse_url = url["url"]
                        try:
                            tools = get_mcp_tools(sse_url)
                            if tools:
                                tools_json = tools_to_json_response(tools)
                                tools_retrieval_success = True
                                logger.info(
                                    f"Successfully retrieved tools from MCP server: {sse_url}"
                                )
                            else:
                                tools_error_message = f"unable to retrieve tools from MCP server at {sse_url}. The server may be unavailable or not responding"
                                logger.warning(
                                    f"Failed to retrieve tools from MCP server: {sse_url}"
                                )

                        except Exception as e:
                            tools_error_message = (
                                f"error connecting to MCP server at {sse_url}: {str(e)}"
                            )
                            logger.error(
                                f"Exception while retrieving tools from {sse_url}: {str(e)}"
                            )
                        break  # Only process the first valid SSE url

                    elif url.get("type") == "streamable-http" and "url" in url:
                        http_url = url["url"]
                        try:
                            logger.info(
                                f"Attempting to retrieve tools via streamable-http from: {http_url}"
                            )
                            # tools = asyncio.run(get_mcp_tools_http(http_url))
                            tools = run_async_from_sync(get_mcp_tools_async(http_url))
                            if tools:
                                tools_json = tools_to_json_response_http(tools)
                                tools_retrieval_success = True
                                logger.info(
                                    f"Successfully retrieved tools from MCP server: {http_url}"
                                )
                            else:
                                tools_error_message = f"unable to retrieve tools from MCP server at {http_url}. The server may be unavailable or not responding"
                                logger.warning(
                                    f"Failed to retrieve tools from MCP server: {http_url}"
                                )

                        except (
                            asyncio.TimeoutError
                        ) as e_timeout:  # Catches timeout from run_async_from_sync's future.result()
                            tools_error_message = f"Timeout retrieving tools from MCP server at {http_url}: {str(e_timeout)}"
                            logger.error(
                                f"Timeout while retrieving tools from {http_url}: {str(e_timeout)}",
                                exc_info=True,
                            )
                        except (
                            asyncio.CancelledError
                        ) as e_cancel:  # Explicitly catch CancelledError
                            tools_error_message = f"Tool retrieval failed for MCP server at {http_url}: {str(e_cancel)}"
                            logger.warning(
                                f"Tool retrieval was failed for {http_url}: {str(e_cancel)}",
                                exc_info=True,
                            )
                        except (
                            Exception
                        ) as e:  # Catches other exceptions propagated by run_async_from_sync
                            tools_error_message = f"Error connecting to or retrieving tools from MCP server at {http_url}: {str(e)}"
                            logger.error(
                                f"Exception while retrieving tools from {http_url}: {str(e)}",
                                exc_info=True,
                            )
                        break

                # git_url, git_branch, github_access_token are optional in this scenario
                git_url = request.git_url or None
                git_branch = request.git_branch or None
                github_access_token = request.github_access_token or None

            # --- Scenario 2: Git details only ---
            elif request.git_url or request.git_branch or request.github_access_token:
                is_git_deployment_scenario = True
                # All three must be present
                if not (request.git_url and request.git_branch and request.github_access_token):
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(
                        "git_url, git_branch, and github_access_token are all required when 'config' is not provided"
                    )
                    return mcp_pb2.MCPResponse(success=False, message="Missing required git fields")
                git_url = request.git_url
                git_branch = request.git_branch
                github_access_token = request.github_access_token
                is_git_deployment_scenario = True
                print(f"[DEBUG] Tools JSON: {github_access_token}")
                # Optionally: Validate git_url format
                if not git_url.startswith("http"):
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details("Invalid git_url format")
                    return mcp_pb2.MCPResponse(success=False, message="Invalid git_url format")
                config = None  # Explicitly set to None

            # --- Neither scenario satisfied: Reject ---
            else:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(
                    "Either 'config' (with at least one entry) or all of 'git_url', 'git_branch', 'github_access_token' must be provided"
                )
                return mcp_pb2.MCPResponse(success=False, message="Invalid MCP creation request")

            new_mcp = McpConfig(
                logo=request.logo if request.logo else None,
                name=request.name,
                description=request.description,
                owner_id=request.owner.id,
                owner_type=mcp_pb2.OwnerType.Name(request.owner_type).lower(),
                user_ids=list(request.user_ids) if request.user_ids else [],
                config=config if config else None,
                git_url=git_url,
                git_branch=git_branch,
                deployment_status=DeploymentStatus.PENDING.value,
                category=mcp_pb2.MCPCategory.Name(request.category).lower(),
                visibility=mcp_pb2.Visibility.Name(request.visibility).lower(),
                tags=list(request.tags) if request.tags else [],
                status=mcp_pb2.Status.Name(request.status).lower(),
                component_category=(
                    request.component_category if request.component_category else None
                ),
            )

            if request.env_keys:
                new_mcp.env_keys = [
                    {"key": ek.key, "description": ek.description} for ek in request.env_keys
                ]
                print(f"[ENV KEYS RECIEVED] {new_mcp.env_keys}")

            if request.oauth_details and request.oauth_details.provider and request.oauth_details.tool_name:
                new_mcp.oauth_details = {
                    "provider": request.oauth_details.provider,
                    "tool_name": request.oauth_details.tool_name,
                }
                print(f"[OAUTH DETAILS RECEIVED] {new_mcp.oauth_details}")

            db.add(new_mcp)
            db.flush()

            if tools_json:
                print(f"[DEBUG TOOLS RETRIVED] {tools_json}")
                save_tools_to_db(db, new_mcp.id, tools_json)

            existing_assignment = (
                db.query(UserMcpAssignment)
                .filter_by(user_id=request.owner.id, mcp_id=new_mcp.id)
                .first()
            )

            if not existing_assignment:
                initial_env_status = EnvCredentialStatus.NOT_REQUIRED if not new_mcp.env_keys else EnvCredentialStatus.PENDING_INPUT

                new_assignment = UserMcpAssignment(
                    user_id=request.owner.id,
                    mcp_id=new_mcp.id,
                    env_credential_status=initial_env_status
                )
                db.add(new_assignment)
                print(f"[DEBUG] {new_assignment} added with status {initial_env_status}")

            db.commit()
            db.refresh(new_mcp)

            # --- Conditional Publishing for Scenario 2 ---
            if is_git_deployment_scenario and mcp_type in ["stdio", "streamable-http"]:
                if not new_mcp.git_url or not new_mcp.git_branch or not github_access_token:
                    logger.error(
                        "Internal logic error: Git deployment scenario active but required git info missing for publishing."
                    )

                else:
                    auth_repo_url = new_mcp.git_url.replace(
                        "https://", f"https://oauth2:{github_access_token}@"
                    )
                    payload = {
                        "id": str(new_mcp.id),
                        "repo_url": auth_repo_url,
                        "branch": new_mcp.git_branch,
                        "github_access_token": github_access_token,
                        "service_name": settings.GOOGLE_SERVICE_NAME,
                        "project_id": settings.GOOGLE_CLOUD_PROJECT_ID,
                        "region": settings.REGION,
                        "memory": "512Mi",
                        "cpu": "1000m",
                        "max_instances": 100,
                        "env_vars": {},
                        "allow_unauthenticated": True,
                        "mcp_type": mcp_type,
                    }

                    try:
                        logger.info(
                            f"Publishing deployment message for MCP ID: {new_mcp.id} & MCP Type : {mcp_type}"
                        )
                        publish_deployment_message(topic_name=settings.TOPIC_NAME, payload=payload)
                        logger.info(
                            f"Successfully published deployment message for MCP ID: {new_mcp.id}"
                        )
                    except Exception as e:
                        logger.error(
                            f"Error publishing deployment message for MCP ID {new_mcp.id}: {str(e)}",
                            exc_info=True,
                        )

            logger.info(f"gRPC: mcp_created with id: {str(new_mcp.id)}")

            # Create appropriate success message based on tools retrieval
            if tools_retrieval_success:
                success_message = (
                    f"MCP '{request.name}' created successfully with tools configured."
                )
            elif tools_error_message:
                success_message = f"MCP '{request.name}' created successfully, but {tools_error_message}. You can update the tools configuration later."
            else:
                success_message = f"MCP '{request.name}' created successfully."

            return mcp_pb2.MCPResponse(
                success=True,
                message=success_message,
                mcp=self._mcp_to_protobuf(db, new_mcp),
            )

        except Exception as db_error:  # Catch specific DB errors if possible
            db.rollback()
            logger.error(
                f"gRPC: database_operation_failed for MCP '{request.name}': {str(db_error)}",
                exc_info=True,
            )
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(
                f"Failed to store MCP: An internal error occurred."
            )  # Avoid exposing raw DB error
            return mcp_pb2.MCPResponse(
                success=False, message=f"Failed to store MCP due to an internal error."
            )
        finally:
            if db:
                db.close()

    def getMCP(
        self, request: mcp_pb2.GetMCPRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.MCPResponse:
        """
        Retrieves an MCP configuration by its unique ID.

        Args:
            request (mcp_pb2.GetMCPConfigRequest): The request containing the MCP configuration ID.
            context (grpc.ServicerContext): The gRPC context for error handling and metadata.

        Returns:
            mcp_pb2.MCPConfigResponse: The response containing the requested MCP configuration details.
        """
        db = self.get_db()
        logger.info("get_mcp_config_request", mcp_id=request.id)
        try:
            mcp_config = db.query(McpConfig).filter(McpConfig.id == request.id).first()
            if mcp_config is None:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("MCP Config not found")
                logger.error("mcp_config_not_found", mcp_id=request.id)
                return mcp_pb2.MCPResponse(success=False, message="MCP Config not found")

            logger.info("mcp_config_retrieved", mcp_id=mcp_config.id)

            # Check if user_id is provided and determine is_added status
            is_added = False
            env_credential_status = EnvCredentialStatus.PENDING_INPUT.value # Default to PENDING_INPUT
            try:
                if hasattr(request, "user_id") and request.HasField("user_id") and request.user_id:
                    user_id = request.user_id

                    # Determine initial status based on whether env_keys are required
                    if not mcp_config.env_keys:
                         env_credential_status = EnvCredentialStatus.NOT_REQUIRED.value
                    else:
                         env_credential_status = EnvCredentialStatus.PENDING_INPUT.value


                    # Check if user has added this MCP to their workspace via UserMcpAssignment table
                    from app.models.mcp_schema import UserMcpAssignment

                    existing_assignment = (
                        db.query(UserMcpAssignment)
                        .filter_by(user_id=user_id, mcp_id=str(mcp_config.id))
                        .first()
                    )
                    is_added = existing_assignment is not None and mcp_config.owner_id != user_id
                    # Check if user is the owner (for logging purposes)
                    is_owner = mcp_config.owner_id == user_id

                    # If an assignment exists, use its status, otherwise use the determined initial status
                    env_credential_status = (
                        existing_assignment.env_credential_status.value
                        if existing_assignment
                        else env_credential_status # Use the status determined based on env_keys
                    )

                    print("env_credential_status: ", env_credential_status)

                    logger.info(
                        "checking_user_mcp_status",
                        user_id=user_id,
                        mcp_id=mcp_config.id,
                        is_owner=is_owner,
                        is_added=is_added,
                        assignment_exists=existing_assignment is not None,
                    )
                else:
                    logger.info(
                        "no_user_id_provided_for_mcp_status",
                        mcp_id=mcp_config.id,
                        is_added=False,
                    )
            except Exception as e:
                # Fallback: if there's an error, default to safe values
                logger.warning(
                    "error_checking_user_mcp_status",
                    mcp_id=mcp_config.id,
                    error=str(e),
                    fallback_is_added=False,
                )
                is_added = False

            # Get the protobuf representation and set is_added field
            mcp_proto = self._mcp_to_protobuf(db, mcp_config)
            mcp_proto.is_added = is_added
            mcp_proto.env_credential_status = env_credential_status
            return mcp_pb2.MCPResponse(
                success=True,
                message=f"MCP Config {mcp_config.name} retrieved successfully",
                mcp=mcp_proto,
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            return mcp_pb2.MCPResponse(success=False, message=str(e))
        finally:
            db.close()

    def updateMCP(
        self, request: mcp_pb2.UpdateMCPRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.MCPResponse:
        db = self.get_db()
        logger.info(
            f"gRPC updateMCP request for ID: {request.id}, paths: {request.update_mask.paths}"
        )

        try:
            mcp_config = db.query(McpConfig).filter(McpConfig.id == request.id).first()
            if not mcp_config:
                logger.warning(f"MCP with ID {request.id} not found for update.")
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"MCP with ID {request.id} not found.")
                return mcp_pb2.MCPResponse(success=False, message="MCP not found.")

            if not request.update_mask.paths:
                logger.info(
                    f"UpdateMCP called for ID {request.id} with an empty field mask. No changes applied."
                )
                return mcp_pb2.MCPResponse(
                    success=True,
                    message="No fields specified for update. No changes made.",
                    mcp=self._mcp_to_protobuf(db, mcp_config),
                )

            updated_fields = []
            is_git_deployment_scenario = False
            update_mask_set = set(request.update_mask.paths)
            tools_retrieval_success = False
            tools_error_message = None

            # --- Scenario 1: config present (primary) ---
            if "config" in update_mask_set:
                config = [{"url": u.url, "type": u.type} for u in request.config]
                if not config or not all("url" in u and "type" in u for u in config):
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(
                        "Each url must have 'url' and 'type', and at least one url must be provided"
                    )
                    return mcp_pb2.MCPResponse(success=False, message="Invalid config structure")
                mcp_config.config = config
                updated_fields.append("config")
                # Optionally update tools config if needed
                for url in config:
                    if url.get("type") == "sse" and "url" in url:
                        sse_url = url["url"]
                        try:
                            tools = get_mcp_tools(sse_url)
                            if tools:
                                tools_json = tools_to_json_response(tools)
                                if tools_json:
                                    print(f"[DEBUG TOOLS RETRIVED] {tools_json}")
                                    save_tools_to_db(db, request.id, tools_json)

                                tools_retrieval_success = True
                                logger.info(
                                    f"Successfully updated tools from MCP server: {sse_url}"
                                )
                            else:
                                tools_error_message = f"unable to retrieve tools from MCP server at {sse_url}. The server may be unavailable or not responding"
                                logger.warning(
                                    f"Failed to retrieve tools during update from MCP server: {sse_url}"
                                )
                        except Exception as e:
                            tools_error_message = (
                                f"error connecting to MCP server at {sse_url}: {str(e)}"
                            )
                            logger.error(f"Exception while updating tools from {sse_url}: {str(e)}")
                        break
                # Optionally update git fields if present in update mask
                if "git_url" in update_mask_set:
                    if not request.git_url.startswith("http"):
                        context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                        context.set_details("Invalid git_url format")
                        return mcp_pb2.MCPResponse(success=False, message="Invalid git_url format")
                    mcp_config.git_url = request.git_url
                    updated_fields.append("git_url")
                if "git_branch" in update_mask_set:
                    mcp_config.git_branch = request.git_branch
                    updated_fields.append("git_branch")

            # --- Scenario 2: Git details only (all three must be provided together) ---
            elif all(
                f in update_mask_set for f in ("git_url", "git_branch", "github_access_token")
            ):
                is_git_deployment_scenario = True
                # All three git fields are being updated together - this is valid
                if not (request.git_url and request.git_branch and request.github_access_token):
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(
                        "git_url, git_branch, and github_access_token must all be non-empty"
                    )
                    return mcp_pb2.MCPResponse(
                        success=False, message="Missing required git fields for update"
                    )
                if not request.git_url.startswith("http"):
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details("Invalid git_url format")
                    return mcp_pb2.MCPResponse(success=False, message="Invalid git_url format")
                mcp_config.git_url = request.git_url
                mcp_config.git_branch = request.git_branch
                github_access_token = request.github_access_token
                updated_fields.extend(["git_url", "git_branch", "github_access_token"])
                # Remove config if present
                mcp_config.config = []

            # --- Scenario 3: Partial Git field update (not allowed) ---
            elif any(
                f in update_mask_set for f in ("git_url", "git_branch", "github_access_token")
            ):
                # Some but not all git fields are being updated - this is not allowed
                missing = [
                    f
                    for f in ("git_url", "git_branch", "github_access_token")
                    if f not in update_mask_set
                ]
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(
                    f"All of git_url, git_branch, and github_access_token must be provided when updating as a Git-based MCP. Missing: {', '.join(missing)}"
                )
                return mcp_pb2.MCPResponse(
                    success=False, message="Missing required git fields for update"
                )

            # --- Handle other fields as before ---
            for field_path in update_mask_set:
                if field_path == "name":
                    mcp_config.name = request.name
                    updated_fields.append("name")
                elif field_path == "logo":
                    mcp_config.logo = request.logo
                    updated_fields.append("logo")
                elif field_path == "description":
                    mcp_config.description = request.description
                    updated_fields.append("description")
                elif field_path == "visibility":
                    mcp_config.visibility = mcp_pb2.Visibility.Name(request.visibility).lower()
                    updated_fields.append("visibility")
                elif field_path == "user_ids":
                    mcp_config.user_ids = list(request.user_ids)
                    updated_fields.append("user_ids")
                elif field_path == "category":
                    mcp_config.category = mcp_pb2.MCPCategory.Name(request.category).lower()
                    updated_fields.append("category")
                elif field_path == "component_category":
                    mcp_config.component_category = request.component_category
                    updated_fields.append("component_category")
                elif field_path == "tags":
                    mcp_config.tags = list(request.tags) if request.tags else []
                    updated_fields.append("tags")
                elif field_path == "status":
                    mcp_config.status = mcp_pb2.Status.Name(request.status).lower()
                    updated_fields.append("status")
                # (config/git fields already handled above)
                elif field_path == "env_keys":
                    mcp_config.env_keys = [
                        {"key": ek.key, "description": ek.description} for ek in request.env_keys
                    ]
                    updated_fields.append("env_keys")
                elif field_path == "oauth_details":
                    if request.oauth_details and request.oauth_details.provider and request.oauth_details.tool_name:
                        mcp_config.oauth_details = {
                            "provider": request.oauth_details.provider,
                            "tool_name": request.oauth_details.tool_name,
                        }
                        updated_fields.append("oauth_details")

            if updated_fields:
                mcp_config.updated_at = datetime.now(timezone.utc)
                db.commit()
                db.refresh(mcp_config)
                logger.info(f"MCP {request.id} updated successfully. Fields: {updated_fields}")
                # --- Conditional Publishing for Scenario 2 ---
                if is_git_deployment_scenario:
                    if (
                        not mcp_config.git_url
                        or not mcp_config.git_branch
                        or not github_access_token
                    ):
                        logger.error(
                            "Internal logic error: Git deployment scenario active but required git info missing for publishing."
                        )

                    else:
                        payload = {
                            "id": str(mcp_config.id),
                            "repo_url": mcp_config.git_url,
                            "branch": mcp_config.git_branch,
                            "github_access_token": github_access_token,
                            "service_name": settings.GOOGLE_SERVICE_NAME,
                            "project_id": settings.GOOGLE_CLOUD_PROJECT_ID,
                            "region": settings.REGION,
                            "memory": "512Mi",
                            "cpu": "1000m",
                            "max_instances": 100,
                            "env_vars": {},
                            "allow_unauthenticated": True,
                        }

                        try:
                            logger.info(
                                f"Publishing deployment message for MCP ID: {mcp_config.id} (Git scenario)"
                            )
                            publish_deployment_message(
                                topic_name=settings.TOPIC_NAME, payload=payload
                            )
                            logger.info(
                                f"Successfully published deployment message for MCP ID: {mcp_config.id}"
                            )
                        except Exception as e:
                            logger.error(
                                f"Error publishing deployment message for MCP ID {mcp_config.id}: {str(e)}",
                                exc_info=True,
                            )

                # Create appropriate success message based on tools retrieval
                base_message = f"MCP '{mcp_config.name}' updated successfully. Fields updated: {', '.join(updated_fields)}"
                if "config" in updated_fields:
                    if tools_retrieval_success:
                        success_message = (
                            f"{base_message}. Tools configuration updated successfully."
                        )
                    elif tools_error_message:
                        success_message = f"{base_message}, but {tools_error_message}. You can update the tools configuration later."
                    else:
                        success_message = base_message
                else:
                    success_message = base_message

                return mcp_pb2.MCPResponse(
                    success=True,
                    message=success_message,
                    mcp=self._mcp_to_protobuf(db, mcp_config),
                )
            else:
                logger.info(
                    f"MCP {request.id} - No recognized fields in update_mask. No database changes committed."
                )
                return mcp_pb2.MCPResponse(
                    success=True,
                    message="No recognized fields in update_mask. No changes made.",
                    mcp=self._mcp_to_protobuf(db, mcp_config),
                )

        except Exception as e:
            db.rollback()
            logger.error(f"Error updating MCP {request.id}: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error while updating MCP: {str(e)}")
            return mcp_pb2.MCPResponse(success=False, message="Internal server error.")
        finally:
            if db:
                db.close()

    def deleteMCP(self, request: mcp_pb2.DeleteMCPRequest, context) -> mcp_pb2.DeleteMCPResponse:
        """
        Deletes an MCP configuration from the database.

        Args:
            request (mcp_pb2.DeleteMCPConfigRequest): The request object containing the MCP ID to be deleted.
            context (grpc.ServicerContext): The gRPC context for handling request status and errors.

        Returns:
            mcp_pb2.DeleteMCPConfigResponse: A response object indicating the success or failure of the deletion.

        Raises:
            grpc.StatusCode.NOT_FOUND: If the specified MCP configuration does not exist.
            grpc.StatusCode.INTERNAL: If an internal error occurs during the deletion process.
        """
        db = self.get_db()
        try:
            # Get MCP config (only non-deleted ones)
            mcp_config = (
                db.query(McpConfig)
                .filter(
                    McpConfig.id == request.id,
                    McpConfig.deleted_at.is_(None),  # Not already deleted
                )
                .first()
            )

            if not mcp_config:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                return mcp_pb2.DeleteMCPResponse(success=False, message="MCP not found")

            # Case 1: Owner deleting entire MCP
            if request.user_id == mcp_config.owner_id:
                mcp_config.deleted_at = func.now()  # SQL NOW()
                message = f"MCP {mcp_config.name} soft-deleted"

                # Optional: Also mark all assignments as deleted
                db.query(UserMcpAssignment).filter(
                    UserMcpAssignment.mcp_id == request.id, UserMcpAssignment.deleted_at.is_(None)
                ).update({"deleted_at": func.now()})
                logger.info(f"Soft-deleting MCP {request.id} for user {request.user_id}")
            # Case 2: User removing from their workspace
            else:
                print(f"scenario 2 : User removing from their workspace")
                assignment = (
                    db.query(UserMcpAssignment)
                    .filter(
                        UserMcpAssignment.mcp_id == request.id,
                        UserMcpAssignment.user_id == request.user_id,
                        UserMcpAssignment.deleted_at.is_(None),
                    )
                    .first()
                )
                print(f"[ashkfjhskjafhjksdf] {assignment}")
                if not assignment:
                    context.set_code(grpc.StatusCode.NOT_FOUND)
                    return mcp_pb2.DeleteMCPResponse(success=False, message="Assignment not found")

                assignment.deleted_at = func.now()
                message = "MCP removed from your workspace"
                logger.info(f"Soft-deleting MCP {request.id} for user {request.user_id}")
            db.commit()
            return mcp_pb2.DeleteMCPResponse(success=True, message=message)

        except Exception as e:
            print(f"[ERROR] {e}")
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            return mcp_pb2.DeleteMCPResponse(success=False, message=f"Deletion failed: {str(e)}")
        finally:
            db.close()

    def listMCPs(
        self, request: mcp_pb2.ListMCPsRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.ListMCPsResponse:
        db = self.get_db()
        logger.info(
            f"gRPC listMCPs request: page={request.page}, page_size={request.page_size}, filters applied."
        )
        page = request.page if request.page > 0 else 1
        page_size = request.page_size if request.page_size > 0 else 10

        try:
            query = db.query(McpConfig)

            # Apply user_id filter: match owner_id or assignment in UserMcpAssignment
            if request.HasField("owner_id") and request.owner_id:
                from app.models.mcp_schema import UserMcpAssignment

                assignment_subquery = select(UserMcpAssignment.mcp_id).filter(
                    UserMcpAssignment.user_id == request.owner_id,
                    UserMcpAssignment.deleted_at is None
                )
                # Debug: Check what the assignment subquery returns
                assignment_ids = db.execute(assignment_subquery).scalars().all()
                logger.info(f"[DEBUG] Assignment MCP IDs for user {request.owner_id}: {assignment_ids}")

                query = query.filter(
                    or_(
                        McpConfig.owner_id == request.owner_id,
                        McpConfig.id.in_(assignment_subquery),
                    )
                )

                # Debug: Check how many MCPs match after owner_id filter
                count_after_owner_filter = query.count()
                logger.info(f"[DEBUG] MCPs matching after owner_id filter: {count_after_owner_filter}")

            # Apply category filter
            if request.HasField("category") and request.category:
                try:
                    category_enum = McpCategory[request.category.upper()]
                    query = query.filter(McpConfig.category == category_enum)
                except KeyError:
                    raise ValueError(f"Invalid MCP category: {request.category}")

            # Apply visibility filter
            if request.HasField("visibility") and request.visibility:
                visibility_str = mcp_pb2.Visibility.Name(request.visibility).lower()
                query = query.filter(McpConfig.visibility == McpVisibility[visibility_str.upper()])

            # Apply status filter
            if request.HasField("status") and request.status:
                status_str = mcp_pb2.Status.Name(request.status).lower()
                query = query.filter(McpConfig.status == McpStatus[status_str.upper()])

            # Apply url_type filter
            if request.HasField("url_type") and request.url_type:
                url_type_str = mcp_pb2.UrlType.Name(request.url_type).lower()
                query = query.filter(McpConfig.url_type == UrlType[url_type_str.upper()])

            # --- Search filter by name or description ---
            if hasattr(request, "search") and getattr(request, "search", None):
                search_value = f"%{request.search}%"
                print(f"[FILTER] Applying search filter: {request.search}")
                query = query.filter(
                    or_(
                        McpConfig.name.ilike(search_value),
                        McpConfig.description.ilike(search_value),
                    )
                )

            if request.tags:
                # Convert tags to a list if it's not already
                tags_list = (
                    list(request.tags) if hasattr(request.tags, "__iter__") else [request.tags]
                )
                # Use ANY to check if any of the requested tags are in the MCP's tags array
                for tag in tags_list:
                    query = query.filter(McpConfig.tags.any(tag))

            if request.HasField("deployment_status") and request.deployment_status:
                query = query.filter(
                    McpConfig.deployment_status
                    == DeploymentStatus[request.deployment_status.upper()]
                )

            if request.HasField("component_category") and request.component_category:
                try:
                    component_category = McpComponentCategory[request.component_category.upper()]
                    query = query.filter(McpConfig.component_category == component_category)
                except KeyError:
                    raise ValueError(
                        f"Invalid MCP component category: {request.component_category}"
                    )
            if not request.HasField("component_category"):
                query = query.filter(McpConfig.component_category == None)

            # Apply quick tools filter
            if request.HasField("quick_tools_only") and request.quick_tools_only:
                logger.info(f"[DEBUG] Applying quick tools filter for user: {request.owner_id}")
                if request.HasField("owner_id") and request.owner_id:
                    # Filter to show only quick tools for the specified user
                    quick_tools_subquery = select(UserMcpAssignment.mcp_id).filter(
                        UserMcpAssignment.user_id == request.owner_id,
                        UserMcpAssignment.is_quick_tool == True,
                        UserMcpAssignment.deleted_at.is_(None)
                    )
                    # Debug: Check what the subquery returns
                    quick_tool_ids = db.execute(quick_tools_subquery).scalars().all()
                    logger.info(f"[DEBUG] Quick tool MCP IDs for user {request.owner_id}: {quick_tool_ids}")
                    query = query.filter(McpConfig.id.in_(quick_tools_subquery))
                else:
                    logger.info("[DEBUG] No owner_id provided with quick_tools_only=True, returning empty result")
                    # If no user_id provided but quick_tools_only is True, return empty result
                    query = query.filter(False)  # This will return no results
            else:
                logger.info(f"[DEBUG] Quick tools filter not applied. HasField: {request.HasField('quick_tools_only')}, Value: {getattr(request, 'quick_tools_only', None)}")

            total = query.count()
            mcp_configs = (
                query.order_by(McpConfig.created_at.desc())
                .offset((page - 1) * page_size)
                .limit(page_size)
                .all()
            )

            total_pages = (total + page_size - 1) // page_size

            # mcp_protos = [self._mcp_to_protobuf(db, mcp_config) for mcp_config in mcp_configs]
            mcp_protos = []
            user_id = request.owner_id if request.HasField("owner_id") else None

            for mcp_config in mcp_configs:
                mcp_proto = self._mcp_to_protobuf(db, mcp_config)

                is_added = False
                is_quick_tool = False
                env_credential_status = EnvCredentialStatus.PENDING_INPUT.value # Default to PENDING_INPUT

                # Determine initial status based on whether env_keys are required for the MCP
                if not mcp_config.env_keys:
                    env_credential_status = EnvCredentialStatus.NOT_REQUIRED.value
                else:
                    env_credential_status = EnvCredentialStatus.PENDING_INPUT.value


                if user_id:
                    try:
                        existing_assignment = (
                            db.query(UserMcpAssignment)
                            .filter_by(user_id=user_id, mcp_id=str(mcp_config.id))
                            .first()
                        )
                        is_added = (
                            existing_assignment is not None and mcp_config.owner_id != user_id
                        )
                        # If an assignment exists, use its status and quick tool flag
                        if existing_assignment:
                            env_credential_status = existing_assignment.env_credential_status.value
                            is_quick_tool = existing_assignment.is_quick_tool
                    except Exception as e:
                        logger.warning(
                            "error_fetching_assignment_in_listMCPs",
                            mcp_id=mcp_config.id,
                            error=str(e),
                        )

                mcp_proto.is_added = is_added
                mcp_proto.env_credential_status = env_credential_status
                mcp_proto.is_quick_tool = is_quick_tool

                mcp_protos.append(mcp_proto)

            logger.info(
                f"Retrieved {len(mcp_protos)} MCPs for page {page}. Total MCPs matching filters: {total}."
            )
            return mcp_pb2.ListMCPsResponse(
                mcps=mcp_protos,
                total=total,
                page=page,
                total_pages=total_pages,
            )
        except Exception as e:
            logger.error(f"Error listing MCPs: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"An internal error occurred while listing MCPs: {str(e)}")
            return mcp_pb2.ListMCPsResponse()
        finally:
            if db:
                db.close()

    def listMCPsMarketplace(
        self, request: mcp_pb2.GetMarketplaceMCPsRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.ListMCPsResponse:
        db = self.get_db()
        logger.info(
            f"gRPC listMCPs request: page={request.page}, page_size={request.page_size}, filters applied."
        )
        page = request.page if request.page > 0 else 1
        page_size = request.page_size if request.page_size > 0 else 10

        try:
            # Query MCPs with visibility set to PUBLIC
            query = db.query(McpConfig).filter(McpConfig.visibility == McpVisibility.PUBLIC)

            # Apply user_id filter if provided (for specific user requests)
            if request.HasField("user_id") and request.user_id:
                query = query.filter(McpConfig.owner_id == request.user_id)

            # Apply category filter
            if request.HasField("category") and request.category:
                query = query.filter(McpConfig.category == McpCategory[request.category.upper()])

            if request.tags:
                # Convert tags to a list if it's not already
                tags_list = (
                    list(request.tags) if hasattr(request.tags, "__iter__") else [request.tags]
                )
                # Use ANY to check if any of the requested tags are in the MCP's tags array
                for tag in tags_list:
                    query = query.filter(McpConfig.tags.any(tag))
            total = query.count()
            mcp_configs = (
                query.order_by(McpConfig.created_at.desc())
                .offset((page - 1) * page_size)
                .limit(page_size)
                .all()
            )

            total_pages = (total + page_size - 1) // page_size

            mcp_protos = [self._mcp_to_protobuf(db, mcp_config) for mcp_config in mcp_configs]

            logger.info(
                f"Retrieved {len(mcp_protos)} MCPs for page {page}. Total MCPs matching filters: {total}."
            )
            return mcp_pb2.ListMCPsResponse(
                mcps=mcp_protos,
                total=total,
                page=page,
                total_pages=total_pages,
            )
        except Exception as e:
            print(f"[ERROR] Error listing MCPs: {str(e)}")
            logger.error(f"Error listing MCPs: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"An internal error occurred while listing MCPs: {str(e)}")
            return mcp_pb2.ListMCPsResponse()
        finally:
            if db:
                db.close()

    def _mcp_to_protobuf(self, db: Session, mcp: McpConfig) -> mcp_pb2.MCP:
        """
        Converts a McpConfig model instance to its protobuf representation.

        Args:
            mcp (McpConfig): The MCP configuration model instance

        Returns:
            mcp_pb2.MCP: The protobuf representation of the MCP
        """
        return mcp_pb2.MCP(
            id=str(mcp.id),
            logo=mcp.logo if mcp.logo else None,
            name=mcp.name,
            description=mcp.description,
            owner_id=mcp.owner_id,
            image_name=mcp.image_name if mcp.image_name else None,
            env_keys=mcp.env_keys if mcp.env_keys else None,
            user_ids=mcp.user_ids if mcp.user_ids else None,
            owner_type=mcp.owner_type,
            config=json.dumps(mcp.config) if mcp.config else None,
            git_url=mcp.git_url if mcp.git_url else None,
            git_branch=mcp.git_branch if mcp.git_branch else None,
            deployment_status=mcp.deployment_status,
            visibility=mcp.visibility,
            tags=mcp.tags if mcp.tags else [],
            status=mcp.status,
            category=mcp.category,
            component_category=mcp.component_category,
            created_at=mcp.created_at.isoformat(),
            updated_at=mcp.updated_at.isoformat(),
            mcp_tools_config=json.dumps(get_tools_json_from_db(db, mcp.id)),
            oauth_details=mcp.oauth_details if mcp.oauth_details else None,
        )

    def getMCPsByIds(
        self, request: mcp_pb2.GetMCPsByIdsRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.ListMCPsResponse:
        """
        Retrieves multiple MCP configurations by their IDs.

        Args:
            request (mcp_pb2.GetMCPsByIdsRequest): The request containing a list of MCP IDs
            context (grpc.ServicerContext): The gRPC service context

        Returns:
            mcp_pb2.ListMCPsResponse: Response containing the list of requested MCPs
        """
        db = self.get_db()
        logger.info("get_mcps_by_ids_request", ids_count=len(request.ids))

        try:
            # Query MCPs by the provided IDs
            mcps = db.query(McpConfig).filter(McpConfig.id.in_(request.ids)).all()

            # Convert to protobuf format
            mcp_list = [self._mcp_to_protobuf(db, mcp) for mcp in mcps]

            # Log the results
            logger.info("mcps_retrieved_by_ids", count=len(mcp_list))

            return mcp_pb2.ListMCPsResponse(
                mcps=mcp_list, total=len(mcp_list), page=1, total_pages=1
            )
        except Exception as e:
            logger.error("get_mcps_by_ids_error", error=str(e))
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error: {str(e)}")
            return mcp_pb2.ListMCPsResponse()
        finally:
            db.close()

    def UpdateDeploymentStatus(
        self, request: mcp_pb2.UpdateDeploymentStatusRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.MCPResponse:
        """
        Updates the deployment status, image name, and URL of an MCP.

        Args:
            request: Contains the MCP ID, new deployment status, and optional individual fields (type, image_name, error_message, url)
            context: gRPC service context

        Returns:
            MCPResponse with success status, message, and updated MCP
        """
        db = self.get_db()
        logger.info(
            f"gRPC UpdateDeploymentStatus request for ID: {request.id}, "
            f"Type: {request.type if hasattr(request, 'type') else 'N/A'}, "
            f"URL: {request.url if hasattr(request, 'url') else 'N/A'}, "
            f"ImageName: {request.image_name if hasattr(request, 'image_name') else 'N/A'}"
        )

        try:
            # Find the MCP
            mcp_config = db.query(McpConfig).filter(McpConfig.id == request.id).first()
            if not mcp_config:
                logger.warning(f"MCP with ID {request.id} not found for deployment status update.")
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"MCP with ID {request.id} not found.")
                return mcp_pb2.MCPResponse(success=False, message="MCP not found.")

            # 1. Update deployment status (if provided)
            if request.deployment_status:
                if request.deployment_status not in [status.value for status in DeploymentStatus]:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(f"Invalid deployment status: {request.deployment_status}")
                    return mcp_pb2.MCPResponse(success=False, message="Invalid deployment status")
                mcp_config.deployment_status = request.deployment_status
                logger.info(
                    f"Updated deployment status to {request.deployment_status} for MCP {request.id}"
                )

            # 2. Update mcp_config.image_name from request.image_name (if provided)
            if hasattr(request, "image_name") and request.image_name:
                mcp_config.image_name = request.image_name
                logger.info(
                    f"Updated mcp_config.image_name to {request.image_name} for MCP {request.id}"
                )

            tools_retrieval_success = False
            tools_error_message = None

            # 3. Handle type-specific logic (SSE or STDIO) and update mcp_config.config accordingly
            if hasattr(request, "type") and request.type:
                current_type = request.type

                valid_url_types = [url_type.value for url_type in UrlType]
                if current_type not in valid_url_types:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(f"Invalid type provided: {current_type}")
                    return mcp_pb2.MCPResponse(
                        success=False, message=f"Invalid type: {current_type}"
                    )

                url_entry_to_store = None

                # --- SSE Type Logic ---
                if current_type == UrlType.SSE.value:
                    if hasattr(request, "url") and request.url:
                        logger.info(
                            f"Processing type 'sse' for MCP {request.id} with URL: {request.url}"
                        )
                        url_entry_to_store = {"url": request.url, "type": current_type}

                        try:
                            tools = get_mcp_tools(request.url)
                            if tools:
                                tools_json = tools_to_json_response(tools)
                                if tools_json:
                                    save_tools_to_db(db, mcp_config.id, tools_json)
                                tools_retrieval_success = True
                                logger.info(
                                    f"Successfully updated tools configuration for MCP {request.id} from SSE URL."
                                )
                            else:
                                tools_error_message = f"unable to retrieve tools from MCP server at {request.url}. The server may be unavailable or not responding"
                                logger.warning(
                                    f"Failed to retrieve tools for MCP {request.id} from SSE URL: {tools_error_message}"
                                )
                        except Exception as e:
                            tools_error_message = (
                                f"error connecting to MCP server at {request.url}: {str(e)}"
                            )
                            logger.error(
                                f"Exception while updating tools from SSE URL for MCP {request.id}: {str(e)}",
                                exc_info=True,
                            )
                    else:
                        logger.warning(
                            f"Type 'sse' provided for MCP {request.id} but no URL was given."
                        )
                        context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                        context.set_details("URL is required when type is 'sse'.")
                        return mcp_pb2.MCPResponse(
                            success=False, message="URL is required for type 'sse'."
                        )

                # --- STDIO Type Logic ---
                elif current_type == UrlType.STDIO.value:
                    logger.info(f"Processing type 'stdio' for MCP {request.id}")

                    if not mcp_config.image_name:
                        logger.warning(
                            f"Type 'stdio' specified for MCP {request.id}, but mcp_config.image_name is not set."
                        )
                        context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                        context.set_details(
                            "image_name is required for 'stdio' type and is not set for this MCP."
                        )
                        return mcp_pb2.MCPResponse(
                            success=False,
                            message="image_name is required for 'stdio' type and not set.",
                        )

                    url_entry_to_store = {"image_name": mcp_config.image_name, "type": current_type}

                    try:
                        ssh_config = {
                            "ssh_host": settings.DEFAULT_SSH_HOST,
                            "ssh_user": settings.DEFAULT_SSH_USER,
                            "ssh_key_content": settings.DEFAULT_SSH_KEY_CONTENT,
                            "container_name": f"{mcp_config.id}_{mcp_config.owner_id}",
                        }

                        # Use a single SSHDockerService instance for all container operations
                        async def _perform_container_operations():
                            async with SSHDockerService(ssh_config) as ssh_service:
                                # Create container
                                logger.info(
                                    f"Attempting to create Docker container '{ssh_service.container_name}' "
                                    f"with image '{mcp_config.image_name}'."
                                )
                                container_id = await ssh_service.create_container(
                                    mcp_config.image_name
                                )
                                logger.info(
                                    f"Docker container '{ssh_config['container_name']}' (ID: {container_id}) created successfully."
                                )

                                # Fetch tools via SSH
                                ssh_client = SSHMCPClient(**ssh_config)
                                fetched_tools = await ssh_client.fetch_tools()
                                logger.info(
                                    f"Tools fetched via SSH for MCP {request.id} (stdio): {fetched_tools}"
                                )

                                if fetched_tools:
                                    tools_json = tools_to_json_response_stdio(fetched_tools)
                                    if tools_json:
                                        save_tools_to_db(db, mcp_config.id, tools_json)
                                    logger.info(
                                        f"Tools configuration updated via SSH for MCP {request.id}"
                                    )

                                # Create deployment record
                                deployment = McpDeployment(
                                    id=str(uuid.uuid4()),
                                    container_name=ssh_config["container_name"],
                                    status=ContainerStatus.RUNNING.value, # Use Enum
                                    user_id=mcp_config.owner_id,
                                    mcp_id=mcp_config.id,
                                    image_name=mcp_config.image_name,
                                    # Removed env_vars as it's not in McpDeployment model
                                )
                                db.add(deployment)
                                db.commit()
                                logger.info(
                                    f"McpDeployment record created for MCP {request.id} (stdio)."
                                )

                                # Stop container
                                logger.info(f"Stopping container '{ssh_service.container_name}'...")
                                stop_result = await ssh_service.stop_container(
                                    force=False, timeout=10
                                )
                                logger.info(f"Container stop result: {stop_result}")
                                deployment.status = ContainerStatus.STOPPED.value # Use Enum
                                db.commit()

                                # Delete container
                                logger.info(f"Deleting container '{ssh_service.container_name}'...")
                                delete_result = await ssh_service.delete_container(force=False)
                                logger.info(f"Container delete result: {delete_result}")
                                deployment.status = ContainerStatus.DELETED.value # Use Enum
                                db.commit()

                                return True

                        # Run all container operations in a single async context
                        container_ops_success = asyncio.run(_perform_container_operations())
                        if not container_ops_success:
                            raise Exception("Container operations failed")

                    except Exception as e:
                        logger.error(
                            f"Failed container deployment for MCP {request.id} (stdio): {str(e)}",
                            exc_info=True,
                        )
                        context.set_code(grpc.StatusCode.INTERNAL)
                        context.set_details(f"Container deployment failed for stdio: {str(e)}")
                        return mcp_pb2.MCPResponse(
                            success=False,
                            message=f"Container deployment failed for stdio: {str(e)}",
                        )

                # Update mcp_config.config if url_entry_to_store was set for sse or stdio
                if url_entry_to_store:
                    if mcp_config.config is None:
                        mcp_config.config = [url_entry_to_store]
                    else:
                        existing_config_list = (
                            mcp_config.config if isinstance(mcp_config.config, list) else []
                        )
                        updated_existing_entry = False
                        for i, existing_item in enumerate(existing_config_list):
                            if (
                                isinstance(existing_item, dict)
                                and existing_item.get("type") == current_type
                            ):
                                existing_config_list[i] = url_entry_to_store
                                updated_existing_entry = True
                                logger.info(
                                    f"Updated URL entry for type '{current_type}' for MCP {request.id}."
                                )
                                break
                        if not updated_existing_entry:
                            existing_config_list.append(url_entry_to_store)
                            logger.info(
                                f"Added new URL entry for type '{current_type}' for MCP {request.id}."
                            )
                        mcp_config.config = existing_config_list

            # 4. Handle error message from request (if provided)
            request_error_message_info = ""
            if hasattr(request, "error_message") and request.error_message:
                request_error_message_info = request.error_message
                logger.warning(
                    f"Error message provided in request for MCP {request.id}: {request_error_message_info}"
                )

            # 5. Update the timestamp
            mcp_config.updated_at = datetime.now(timezone.utc)

            # 6. Commit all changes to the database
            db.commit()
            db.refresh(mcp_config)

            # 7. Create appropriate success message
            success_message_parts = [f"MCP {request.id} update processed."]
            if request.deployment_status:
                success_message_parts.append(
                    f"Deployment status set to '{mcp_config.deployment_status}'."
                )

            if request_error_message_info:
                success_message_parts.append(
                    f"Caller reported error: '{request_error_message_info}'."
                )

            if tools_retrieval_success:
                success_message_parts.append("Tools configuration updated successfully from SSE.")
            elif tools_error_message:
                success_message_parts.append(
                    f"SSE tools issue: {tools_error_message}. You may need to update tools later."
                )

            final_success_message = " ".join(success_message_parts)
            logger.info(
                f"Successfully processed UpdateDeploymentStatus for MCP {request.id}. Message: {final_success_message}"
            )

            return mcp_pb2.MCPResponse(
                success=True,
                message=final_success_message,
                mcp=self._mcp_to_protobuf(db, mcp_config),
            )

        except Exception as e:
            db.rollback()
            logger.error(
                f"Unhandled error updating deployment status for MCP {request.id}: {str(e)}",
                exc_info=True,
            )
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"An internal error occurred: {str(e)}")
            return mcp_pb2.MCPResponse(
                success=False,
                message="Failed to update MCP deployment status due to an internal error.",
            )
        finally:
            if db:
                db.close()

    def UpdateToolOutputSchema(
        self, request: mcp_pb2.UpdateToolOutputSchemaRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.MCPResponse:
        db = self.get_db()
        logger.info(
            "gRPC UpdateToolOutputSchema request",
            mcp_id=request.mcp_id,
            tool_name=request.tool_name,
        )

        try:
            mcp_config = db.query(McpConfig).filter(McpConfig.id == request.mcp_id).first()

            if not mcp_config:
                logger.warning(f"MCP with ID {request.mcp_id} not found.")
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"MCP with ID {request.mcp_id} not found.")
                return mcp_pb2.MCPResponse(success=False, message="MCP not found.")

            tool_row = (
                db.query(McpToolConfig)
                .filter(
                    McpToolConfig.mcp_config_id == request.mcp_id,
                    McpToolConfig.name == request.tool_name,
                )
                .first()
            )
            if not tool_row:
                logger.warning(f"Tool '{request.tool_name}' not found in MCP '{request.mcp_id}'.")
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Tool '{request.tool_name}' not found in this MCP.")
                return mcp_pb2.MCPResponse(
                    success=False, message=f"Tool '{request.tool_name}' not found."
                )

            try:
                tool_row.output_schema = json.loads(request.output_schema_json)
                db.commit()
            except json.JSONDecodeError as e:
                logger.error(
                    f"Invalid JSON in output_schema_json for tool '{request.tool_name}', MCP '{request.mcp_id}': {str(e)}"
                )
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details(f"Invalid JSON format for output_schema: {str(e)}")
                return mcp_pb2.MCPResponse(
                    success=False, message="Invalid JSON format for output_schema."
                )

            logger.info(
                f"Successfully updated output_schema for tool '{request.tool_name}' in MCP '{request.mcp_id}'."
            )
            return mcp_pb2.MCPResponse(
                success=True,
                message=f"Output schema for tool '{request.tool_name}' updated successfully.",
                mcp=self._mcp_to_protobuf(db, mcp_config),
            )

        except Exception as e:
            db.rollback()
            logger.error(
                f"Error updating tool output schema for MCP {request.mcp_id}, tool {request.tool_name}: {str(e)}",
                exc_info=True,
            )
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"An internal server error occurred: {str(e)}")
            return mcp_pb2.MCPResponse(
                success=False,
                message="Failed to update tool output schema due to an internal error.",
            )
        finally:
            if db:
                db.close()

    def ToggleMcpVisibility(
        self, request: mcp_pb2.ToggleMcpVisibilityRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.ToggleMcpVisibilityResponse:
        print(
            f"[GRPC_SERVER] ToggleMcpVisibility called for MCP ID: {request.mcp_id} by User ID: {request.user_id}"
        )
        db = self.get_db()
        try:
            mcp = (
                db.query(McpConfig).filter(McpConfig.id == request.mcp_id).first()
            )  # Using mock syntax

            if not mcp:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"MCP with ID '{request.mcp_id}' not found.")
                return mcp_pb2.ToggleMcpVisibilityResponse(
                    success=False, message=f"MCP with ID {request.mcp_id} not found."
                )

            # 2. Check ownership
            if mcp.owner_id != request.user_id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details(
                    f"User '{request.user_context.user_id}' is not the owner of MCP '{request.mcp_id}'."
                )
                return mcp_pb2.ToggleMcpVisibilityResponse(
                    success=False,
                    message=f"User {request.user_id} is not the owner of MCP {request.mcp_id}.",
                )

            # 3. Toggle visibility
            new_visibility_str = ""
            if mcp.visibility == McpVisibility.PRIVATE:
                mcp.visibility = McpVisibility.PUBLIC
                new_visibility_str = "public"
            elif mcp.visibility == McpVisibility.PUBLIC:
                mcp.visibility = McpVisibility.PRIVATE
                new_visibility_str = "private"
            else:
                # Should not happen if data is clean and enums are used
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(
                    f"MCP {request.mcp_id} has an unexpected visibility state: {mcp.visibility}."
                )
                return mcp_pb2.ToggleMcpVisibilityResponse(
                    success=False, message=f"Unexpected visibility state for MCP {request.mcp_id}."
                )

            db.commit()
            print(f"[GRPC_SERVER] MCP '{mcp.id}' visibility changed to {mcp.visibility}")

            return mcp_pb2.ToggleMcpVisibilityResponse(
                success=True,
                message=f"MCP {mcp.id} visibility successfully toggled to {new_visibility_str}.",
            )

        except Exception as e:
            print(
                f"[GRPC_SERVER_ERROR] Exception in ToggleMcpVisibility for MCP {request.mcp_id}: {str(e)}"
            )
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"An internal error occurred: {str(e)}")
            return mcp_pb2.ToggleMcpVisibilityResponse(
                success=False, message=f"internal error occurred: {str(e)}"
            )

        finally:
            if db:
                db.close()

    def UpdateMcpEnvVars(
        self, request: mcp_pb2.UpdateMcpEnvVarsRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.UpdateMcpEnvVarsResponse:
        db = self.get_db()
        try:
            logger.info(
                f"UpdateMcpEnvVars called for user_id: {request.user_id}, mcp_id: {request.mcp_id}"
            )

            # Ensure MCP exists
            mcp_config = db.query(McpConfig).filter(McpConfig.id == request.mcp_id).first()
            if not mcp_config:
                context.set_details(f"MCP Config with ID '{request.mcp_id}' not found.")
                context.set_code(grpc.StatusCode.NOT_FOUND)
                return mcp_pb2.UpdateMcpEnvVarsResponse(
                    success=False, message="MCP Config not found."
                )

            # Build dictionary from repeated EnvVar message
            env_dict = {kv.key: kv.value for kv in request.env_key_values}
            print(f"[DEBUG -> Build dictionary from repeated EnvVar message] {env_dict}")
            env_json_string = json.dumps(env_dict)
            print(f"[DEBUG -> ENV TO JSON STRING] {env_json_string}")

            # Encrypt the JSON string
            try:
                encrypted_blob = secret_manager.encrypt(env_json_string, request.user_id)
            except ValueError as encryption_error:
                if "No encryption key found" in str(encryption_error):
                    logger.warning(
                        f"No encryption key found for user {request.user_id}, creating one..."
                    )
                    secret_id = secret_manager._get_secret_name(request.user_id)
                    secret_manager.create_and_store_user_key(secret_id)
                    encrypted_blob = secret_manager.encrypt(env_json_string, request.user_id)
                else:
                    raise encryption_error

            # Find or create the UserMcpAssignment
            assignment = (
                db.query(UserMcpAssignment)
                .filter_by(user_id=request.user_id, mcp_id=request.mcp_id)
                .first()
            )

            if not assignment:
                # Fetch the MCP config to check for required env_keys
                mcp_config = db.query(McpConfig).filter(McpConfig.id == request.mcp_id).first()
                initial_env_status = EnvCredentialStatus.PENDING_INPUT
                if mcp_config and not mcp_config.env_keys:
                    initial_env_status = EnvCredentialStatus.NOT_REQUIRED

                assignment = UserMcpAssignment(
                    user_id=request.user_id,
                    mcp_id=request.mcp_id,
                    env_variables=encrypted_blob,
                    env_credential_status=initial_env_status, # Set status based on env_keys
                )
                db.add(assignment)
            else:
                assignment.env_variables = encrypted_blob
                assignment.env_credential_status = EnvCredentialStatus.PROVIDED.value # Status is PROVIDED if updating env vars

            db.commit()

            return mcp_pb2.UpdateMcpEnvVarsResponse(
                success=True,
                message="Environment variables updated successfully.",
                user_mcp_assignment_id=assignment.id,
                env_credential_status=assignment.env_credential_status,
            )

        except Exception as e:
            logger.error(f"Unexpected error in UpdateMcpEnvVars: {str(e)}", exc_info=True)
            context.set_details("An unexpected internal error occurred.")
            context.set_code(grpc.StatusCode.INTERNAL)
            db.rollback()
            return mcp_pb2.UpdateMcpEnvVarsResponse(success=False, message="Internal server error.")
        finally:
            db.close()

    def GetMcpEnvVars(
        self, request: mcp_pb2.GetMcpEnvVarsRequest, context: grpc.ServicerContext
    ) -> mcp_pb2.GetMcpEnvVarsResponse:
        db = self.get_db()
        try:
            logger.info(
                f"GetMcpEnvVars called for user_id: {request.user_id}, mcp_id: {request.mcp_id}"
            )

            # Fetch MCP Config
            mcp_config = db.query(McpConfig).filter(McpConfig.id == request.mcp_id).first()
            if not mcp_config:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"MCP Config with ID '{request.mcp_id}' not found.")
                return mcp_pb2.GetMcpEnvVarsResponse(success=False, message="MCP Config not found.")

            # Build defined_env_keys from mcp_config.env_keys
            defined_env_keys = []
            if mcp_config.env_keys:
                for item in mcp_config.env_keys:
                    if isinstance(item, dict) and "key" in item:
                        defined_env_keys.append(
                            mcp_pb2.EnvKey(key=item["key"], description=item.get("description", ""))
                        )

            # Fetch assignment
            assignment = (
                db.query(UserMcpAssignment)
                .filter_by(user_id=request.user_id, mcp_id=request.mcp_id)
                .first()
            )

            env_key_values = []
            user_mcp_assignment_id = assignment.id if assignment else None
            current_status = EnvCredentialStatus.PENDING_INPUT

            if assignment:
                current_status = assignment.env_credential_status

                if assignment.env_variables:
                    try:
                        # Decrypt
                        decrypted_blob = secret_manager.decrypt(
                            assignment.env_variables, request.user_id
                        )
                        decrypted_dict = json.loads(decrypted_blob)
                        env_key_values = [
                            mcp_pb2.EnvVar(
                                key=k,
                                value=secret_manager.encrypt(plain_text=v, user_id=request.user_id),
                            )
                            for k, v in decrypted_dict.items()
                        ]
                    except ValueError as ve:
                        if "No encryption key found" in str(ve):
                            logger.warning(f"No encryption key found for user {request.user_id}.")
                            current_status = EnvCredentialStatus.PENDING_INPUT
                        else:
                            logger.warning(
                                f"Decryption error for user {request.user_id}: {str(ve)}"
                            )
                            current_status = EnvCredentialStatus.PENDING_INPUT
                    except Exception as e:
                        logger.error(f"Unexpected error during decryption: {str(e)}", exc_info=True)
                        current_status = EnvCredentialStatus.PENDING_INPUT

            return mcp_pb2.GetMcpEnvVarsResponse(
                success=True,
                message=(
                    "Environment variables retrieved successfully."
                    if env_key_values
                    else "No environment variables found or decryption failed."
                ),
                env_key_values=env_key_values,
                defined_env_keys=defined_env_keys,
                env_credential_status=current_status.value,
                user_mcp_assignment_id=user_mcp_assignment_id,
            )

        except Exception as e:
            logger.error(f"Unexpected error in GetMcpEnvVars: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("An unexpected internal error occurred.")
            return mcp_pb2.GetMcpEnvVarsResponse(success=False, message="Internal server error.")
        finally:
            db.close()

    def refresh_mcp(self, request: mcp_pb2.RefreshMCPRequest, context: grpc.ServicerContext) -> mcp_pb2.MCPResponse:
        db = self.get_db()
        logger.info(f"gRPC refreshMCP request for ID: {request.mcp_id}")

        try:
            mcp_config = db.query(McpConfig).filter(McpConfig.id == request.mcp_id).first()
            if not mcp_config:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"MCP with ID {request.mcp_id} not found.")
                return mcp_pb2.MCPResponse(success=False, message="MCP not found.")

            config = mcp_config.config or []
            if not config:
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details("MCP has no config URLs.")
                return mcp_pb2.MCPResponse(success=False, message="MCP config is empty.")

            tools_retrieval_success = False
            tools_error_message = None
            tools_json = None

            for url_entry in config:
                url_type = url_entry.get("type")
                url = url_entry.get("url")

                if not url_type or not url:
                    continue

                if url_type == "sse":
                    try:
                        tools = get_mcp_tools(url)
                        if tools:
                            tools_json = tools_to_json_response(tools)
                            tools_retrieval_success = True
                            logger.info(f"[REFRESH] SSE tools retrieved from {url}")
                        else:
                            tools_error_message = f"Failed to retrieve SSE tools from {url} (empty response)"
                            logger.warning(tools_error_message)
                    except Exception as e:
                        tools_error_message = f"Error retrieving SSE tools from {url}: {str(e)}"
                        logger.error(tools_error_message, exc_info=True)
                    break  # stop after first successful or failed attempt

                elif url_type == "streamable-http":
                    try:
                        logger.info(f"[REFRESH] Attempting streamable-http tools from: {url}")
                        tools = run_async_from_sync(get_mcp_tools_async(url))
                        if tools:
                            tools_json = tools_to_json_response_http(tools)
                            tools_retrieval_success = True
                            logger.info(f"[REFRESH] HTTP tools retrieved from {url}")
                        else:
                            tools_error_message = f"Failed to retrieve HTTP tools from {url} (empty response)"
                            logger.warning(tools_error_message)
                    except asyncio.TimeoutError as e:
                        tools_error_message = f"Timeout while retrieving tools from {url}: {str(e)}"
                        logger.error(tools_error_message, exc_info=True)
                    except asyncio.CancelledError as e:
                        tools_error_message = f"Tool retrieval cancelled for {url}: {str(e)}"
                        logger.warning(tools_error_message, exc_info=True)
                    except Exception as e:
                        tools_error_message = f"Error retrieving HTTP tools from {url}: {str(e)}"
                        logger.error(tools_error_message, exc_info=True)
                    break

                elif mcp_config.image_name is not None:
                    # For stdio type, we need image_name from the MCP config or from the url_entry
                    stdio_image_name = mcp_config.image_name
                    if not stdio_image_name:
                        tools_error_message = f"STDIO type MCP requires image_name but none found for MCP {request.mcp_id}"
                        logger.warning(tools_error_message)
                        continue
                    
                    try:
                        logger.info(f"[REFRESH] Processing STDIO type for MCP {request.mcp_id} with image: {stdio_image_name}")
                        
                        ssh_config = {
                            "ssh_host": settings.DEFAULT_SSH_HOST,
                            "ssh_user": settings.DEFAULT_SSH_USER,
                            "ssh_key_content": settings.DEFAULT_SSH_KEY_CONTENT,
                            "container_name": f"{mcp_config.id}_{mcp_config.owner_id}",
                        }

                        # Use a single SSHDockerService instance for all container operations
                        async def _perform_stdio_refresh_operations():
                            async with SSHDockerService(ssh_config) as ssh_service:
                                # Create container
                                logger.info(
                                    f"[REFRESH] Creating Docker container '{ssh_service.container_name}' "
                                    f"with image '{stdio_image_name}' for tools refresh."
                                )
                                container_id = await ssh_service.create_container(stdio_image_name)
                                logger.info(
                                    f"[REFRESH] Docker container '{ssh_config['container_name']}' (ID: {container_id}) created successfully."
                                )

                                # Fetch tools via SSH
                                ssh_client = SSHMCPClient(**ssh_config)
                                fetched_tools = await ssh_client.fetch_tools()
                                logger.info(
                                    f"[REFRESH] Tools fetched via SSH for MCP {request.mcp_id} (stdio): {fetched_tools}"
                                )

                                tools_json_result = None
                                if fetched_tools:
                                    tools_json_result = tools_to_json_response_stdio(fetched_tools)
                                    logger.info(
                                        f"[REFRESH] Tools configuration processed via SSH for MCP {request.mcp_id}"
                                    )

                                # Stop container
                                logger.info(f"[REFRESH] Stopping container '{ssh_service.container_name}'...")
                                stop_result = await ssh_service.stop_container(force=False, timeout=10)
                                logger.info(f"[REFRESH] Container stop result: {stop_result}")

                                # Delete container
                                logger.info(f"[REFRESH] Deleting container '{ssh_service.container_name}'...")
                                delete_result = await ssh_service.delete_container(force=False)
                                logger.info(f"[REFRESH] Container delete result: {delete_result}")

                                return tools_json_result

                        # Run all container operations in a single async context
                        tools_json = asyncio.run(_perform_stdio_refresh_operations())
                        if tools_json:
                            tools_retrieval_success = True
                            logger.info(f"[REFRESH] STDIO tools retrieved successfully for MCP {request.mcp_id}")
                        else:
                            tools_error_message = f"Failed to retrieve STDIO tools for MCP {request.mcp_id} (empty response)"
                            logger.warning(tools_error_message)

                    except Exception as e:
                        tools_error_message = f"Error retrieving STDIO tools for MCP {request.mcp_id}: {str(e)}"
                        logger.error(tools_error_message, exc_info=True)
                    break  # stop after first successful or failed attempt

            if tools_retrieval_success and tools_json:
                save_tools_to_db(db, mcp_config.id, tools_json)
                db.commit()
                return mcp_pb2.MCPResponse(
                    success=True,
                    message="Tools refreshed successfully.",
                    mcp=self._mcp_to_protobuf(db, mcp_config),
                )
            else:
                context.set_code(grpc.StatusCode.UNAVAILABLE)
                context.set_details(tools_error_message or "Tool retrieval failed.")
                return mcp_pb2.MCPResponse(
                    success=False,
                    message=tools_error_message or "Tool retrieval failed.",
                )

        except Exception as e:
            db.rollback()
            logger.error(f"Unexpected error in refresh_mcp for ID {request.mcp_id}: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("Internal server error during refresh.")
            return mcp_pb2.MCPResponse(success=False, message="Internal server error during refresh.")
        finally:
            if db:
                db.close()

    def makeQuickTool(self, request: mcp_pb2.MakeQuickToolRequest, context: grpc.ServicerContext) -> mcp_pb2.QuickToolResponse:
        """
        Makes an MCP a quick tool for a user, with validation for the 4-tool limit.
        """
        db = self.get_db()
        logger.info(f"gRPC makeQuickTool request for MCP ID: {request.mcp_id}, User ID: {request.user_id}")

        try:
            # Check if the MCP exists
            mcp_config = db.query(McpConfig).filter(McpConfig.id == request.mcp_id).first()
            if not mcp_config:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"MCP with ID {request.mcp_id} not found.")
                return mcp_pb2.QuickToolResponse(success=False, message="MCP not found.")

            # Check if user already has an assignment for this MCP
            assignment = db.query(UserMcpAssignment).filter(
                UserMcpAssignment.mcp_id == request.mcp_id,
                UserMcpAssignment.user_id == request.user_id,
                UserMcpAssignment.deleted_at.is_(None)
            ).first()

            if not assignment:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("User does not have access to this MCP.")
                return mcp_pb2.QuickToolResponse(success=False, message="User does not have access to this MCP.")

            # Check if it's already a quick tool
            if assignment.is_quick_tool:
                return mcp_pb2.QuickToolResponse(
                    success=True,
                    message="MCP is already a quick tool.",
                    quick_tools_count=self._get_user_quick_tools_count(db, request.user_id)
                )

            # Check the 4-tool limit
            current_quick_tools_count = self._get_user_quick_tools_count(db, request.user_id)
            if current_quick_tools_count >= 4:
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details("User has reached the maximum limit of 4 quick tools.")
                return mcp_pb2.QuickToolResponse(
                    success=False,
                    message="You have reached the maximum limit of 4 quick tools. Please remove a quick tool before adding a new one.",
                    quick_tools_count=current_quick_tools_count
                )

            # Make it a quick tool
            assignment.is_quick_tool = True
            assignment.updated_at = func.now()
            db.commit()

            new_count = self._get_user_quick_tools_count(db, request.user_id)
            logger.info(f"MCP {request.mcp_id} successfully made a quick tool for user {request.user_id}")

            return mcp_pb2.QuickToolResponse(
                success=True,
                message="MCP successfully added to quick tools.",
                quick_tools_count=new_count
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error making MCP {request.mcp_id} a quick tool for user {request.user_id}: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("Internal server error.")
            return mcp_pb2.QuickToolResponse(success=False, message="Internal server error.")
        finally:
            if db:
                db.close()

    def removeQuickTool(self, request: mcp_pb2.RemoveQuickToolRequest, context: grpc.ServicerContext) -> mcp_pb2.QuickToolResponse:
        """
        Removes an MCP from quick tools for a user.
        """
        db = self.get_db()
        logger.info(f"gRPC removeQuickTool request for MCP ID: {request.mcp_id}, User ID: {request.user_id}")

        try:
            # Find the user's assignment for this MCP
            assignment = db.query(UserMcpAssignment).filter(
                UserMcpAssignment.mcp_id == request.mcp_id,
                UserMcpAssignment.user_id == request.user_id,
                UserMcpAssignment.deleted_at.is_(None)
            ).first()

            if not assignment:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("User does not have access to this MCP.")
                return mcp_pb2.QuickToolResponse(success=False, message="User does not have access to this MCP.")

            # Check if it's currently a quick tool
            if not assignment.is_quick_tool:
                return mcp_pb2.QuickToolResponse(
                    success=True,
                    message="MCP is not currently a quick tool.",
                    quick_tools_count=self._get_user_quick_tools_count(db, request.user_id)
                )

            # Remove from quick tools
            assignment.is_quick_tool = False
            assignment.updated_at = func.now()
            db.commit()

            new_count = self._get_user_quick_tools_count(db, request.user_id)
            logger.info(f"MCP {request.mcp_id} successfully removed from quick tools for user {request.user_id}")

            return mcp_pb2.QuickToolResponse(
                success=True,
                message="MCP successfully removed from quick tools.",
                quick_tools_count=new_count
            )

        except Exception as e:
            db.rollback()
            logger.error(f"Error removing MCP {request.mcp_id} from quick tools for user {request.user_id}: {str(e)}", exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details("Internal server error.")
            return mcp_pb2.QuickToolResponse(success=False, message="Internal server error.")
        finally:
            if db:
                db.close()

    def _get_user_quick_tools_count(self, db: Session, user_id: str) -> int:
        """
        Helper method to get the count of quick tools for a user.
        """
        return db.query(UserMcpAssignment).filter(
            UserMcpAssignment.user_id == user_id,
            UserMcpAssignment.is_quick_tool == True,
            UserMcpAssignment.deleted_at.is_(None)
        ).count()

def save_tools_to_db(db, mcp_id, tools_json):
    # Remove old tool configs
    db.query(McpToolConfig).filter(McpToolConfig.mcp_config_id == mcp_id).delete(synchronize_session=False)
    # Insert new tool configs
    for tool in tools_json.get("tools", []):
        db.add(
            McpToolConfig(
                mcp_config_id=mcp_id,
                name=tool.get("name"),
                description=tool.get("description"),
                input_schema=tool.get("input_schema"),
                output_schema=tool.get("output_schema"),
                required=tool.get("input_schema").get("required"),
                annotations=tool.get("annotations"),
            )
        )
        print(f"[DEBUG] tool name : {tool.get('name')} added in db")
    db.flush()


def get_tools_json_from_db(db, mcp_id):
    tools = db.query(McpToolConfig).filter(McpToolConfig.mcp_config_id == mcp_id).all()
    return {
        "meta": None,
        "nextCursor": None,
        "tools": [
            {
                "name": t.name,
                "description": t.description,
                "input_schema": t.input_schema,
                "output_schema": t.output_schema,
                "annotations": t.annotations,
            }
            for t in tools
        ],
    }
